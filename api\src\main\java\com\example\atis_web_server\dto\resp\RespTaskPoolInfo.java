package com.example.atis_web_server.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "任务池响应实体")
public class RespTaskPoolInfo {
    @ApiModelProperty("任务池中任务id")
    private Long id;
    @ApiModelProperty("任务名称")
    private String name;
    @ApiModelProperty("用户ID")
    private Long uid;
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("任务类型：0命名naming，1提要求answer，2对话任务chat")
    private Short typeId = 2;
    @ApiModelProperty("大模型提示词prompt")
    private String prompt;
    @ApiModelProperty("任务是否被完成过：对应的任务记录中的任务是否有被完成过的")
    private Boolean used = false;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否逻辑删除")
    private Boolean deleted = false;
    @ApiModelProperty("由哪个任务记录对应的聊天记录生成的：-1为默认任务")
    private Integer taskRecordId;
    @ApiModelProperty("医生姓名")
    private String doctorName;  // admin
    @ApiModelProperty("医生ID")
    private String doctorId; // admin
}
