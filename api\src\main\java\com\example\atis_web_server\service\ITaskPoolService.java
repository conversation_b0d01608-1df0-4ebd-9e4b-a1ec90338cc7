package com.example.atis_web_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.atis_web_server.dto.query.PageDTO;

import com.example.atis_web_server.dto.req.ReqTaskPoolInfo;
import com.example.atis_web_server.dto.req.ReqUpdateTaskPool;
import com.example.atis_web_server.dto.resp.RespTaskPoolInfo;
import com.example.atis_web_server.pojo.TaskPoolModel;

import java.io.IOException;
import java.util.List;

public interface ITaskPoolService extends IService<TaskPoolModel> {

    void initTaskPool(Long uid, Long doctorId) throws IOException;

    void updateDefaultTaskPool() throws IOException;

    List<TaskPoolModel> selectByUsed(Long uid, <PERSON>olean used);

    void generateTaskFromAI(Long uid, Long doctorId) throws Exception;

    TaskPoolModel toTaskPoolModel(ReqUpdateTaskPool req);

    PageDTO<RespTaskPoolInfo> queryTaskPoolPage(ReqTaskPoolInfo req, Long doctorId);

    boolean isExistsTaskRPoolById(Long Id);
}
