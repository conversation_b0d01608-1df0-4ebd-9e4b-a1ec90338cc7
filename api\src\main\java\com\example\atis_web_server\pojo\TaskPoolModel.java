package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableName;


@TableName(ConstModelNameAttribute.TaskPool)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskPoolModel {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("任务池中任务id")
    private Long id;
    @ApiModelProperty("任务名称")
    private String name;
    @ApiModelProperty("用户ID")
    private Long uid;
    @ApiModelProperty("任务类型：0命名naming，1提要求answer，2对话任务chat")
    private Short typeId = 2;
    @ApiModelProperty("任务的具体内容，用于大模型提示词prompt")
    private String prompt;
    @ApiModelProperty("任务是否被完成过：对应的任务记录中的任务是否有被完成过的")
    private Boolean used = false;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否逻辑删除")
    private Boolean deleted = false;
    @ApiModelProperty("由哪个任务记录对应的聊天记录生成的：-1为默认任务")
    private Integer taskRecordId;
    @ApiModelProperty("医生ID")
    private Long doctorId;
}
