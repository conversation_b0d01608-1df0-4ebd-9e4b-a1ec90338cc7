package com.example.atis_web_server.controller;

import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.service.impl.SurveyService;
import com.example.atis_web_server.utils.Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 待删除  甲方说不要问卷功能了 TODO
 */
@Api(tags = "客户端问卷管理接口")
@Slf4j
@RestController
@RequestMapping(value = "/client")
public class SurveyController {
    @Autowired
    private SurveyService surveyService;

    private static final Logger logger = LoggerFactory.getLogger(SurveyController.class);

    /**
     * 保存问卷结果
     *
     * @param survey Map
     * @param token  String
     * @return Result
     */
    @ApiOperation("保存问卷记录")
    @PostMapping("/QuestionnaireUrl")
    public Result saveSurveyRecord(@Valid @RequestBody Map<String, String> survey, @RequestHeader(Utils.TOKEN) String token) {
        Long Uid = Utils.getUidByToken(token);
        surveyService.saveSurveyRecord(Uid, survey.get("result"));
        return Result.success();
    }

    /**
     * 查询当天是否做了问卷
     *
     * @return Result
     */
    @ApiOperation("检查当天是否做了问卷")
    @PostMapping("/IsQuestionnaire")
    public Result checkSurveyIsDone(@RequestHeader(Utils.TOKEN) String token) {
        Long Uid = Utils.getUidByToken(token);
        // 创建一个 Map 来存储 token
        Map<String, Object> map = new HashMap<>();
        map.put("IsQuestionnaire", surveyService.getIsDoSurveyToday(Uid));
        return Result.success(map);
    }

}
