package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName(ConstModelNameAttribute.Config)
@Data
@NoArgsConstructor
@AllArgsConstructor
// 患者满意度调查问卷
public class ConfigModel {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("ID")
    private Long id;
    @ApiModelProperty("root密码")
    private String RootPwd;
    @ApiModelProperty("gpt api key")
    private String GptKey;
    @ApiModelProperty("gpt api url")
    private String GptUrl;
    @ApiModelProperty("语音服务令牌")
    private String AzureSubscriptionKey;
    @ApiModelProperty("语音服务地区码")
    private String AzureServiceRegion;
    @ApiModelProperty("语音服务语言")
    private String AzureLanguage;
    @ApiModelProperty("用户说完话的时间参数")
    private Float MicSilenceThreshold;
    @ApiModelProperty("麦克风最大录音时长")
    private Integer MicMaxRecordingSeconds;
    @ApiModelProperty("网络出错尝试次数")
    private Integer NetMaxAttempts;
    @ApiModelProperty("网络请求超时")
    private Integer NetTimeout;
    @ApiModelProperty("VAD检测模式")
    private Integer VadMode;
    @ApiModelProperty("聊天任务时间（s）")
    private Integer ChatTime;
}
