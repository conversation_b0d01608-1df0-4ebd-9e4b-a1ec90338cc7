package com.example.atis_web_server.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "任务记录响应实体")
public class RespTaskRecordInfo {
    @ApiModelProperty("任务记录ID")
    private Long id;
    @ApiModelProperty("任务ID")
    private Long tid;
    @ApiModelProperty("任务名")
    private String name;
    @ApiModelProperty("用户ID")
    private Long uid;
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("任务完成状态")
    private Boolean completion;
    @ApiModelProperty("完成时间")
    private LocalDateTime completeTime;
    @ApiModelProperty("任务得分")
    private Integer score;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("第几天的任务")
    private Integer day;
    @ApiModelProperty("设定任务最大完成时间")
    private Integer time;
    @ApiModelProperty("是否被用于生成过新话题")
    private Boolean used;
    @ApiModelProperty("医生姓名")
    private String doctorName;  // admin
    @ApiModelProperty("医生ID")
    private Long doctorId; // admin
}