import axios from 'axios'
import { UserModule } from '@/store/modules/user'
import {getRequestKey,removePending} from './requestOptimize'
import router from '@/router'

// 创建 axios 实例
const service = axios.create({
  baseURL: '/api',              // 基础 URL，所有请求都会加上 /api 前缀
  'timeout': 600000             // 请求超时时间，单位为毫秒
})

// 请求拦截器
service.interceptors.request.use(
  (config: any) => {
    // 如果用户模块中有 token，则在请求头中添加 token
    if (UserModule.token) {
      config.headers['Authorization'] = UserModule.token
    } else if (UserModule.token && config.url != '/login') {
      // 如果没有 token 且请求的不是登录接口，则跳转到登录页面
      window.location.href = '/login'
      return false
    }
    return config
  },
  (error: any) => {
    // 请求错误时返回 Promise 拒绝
    Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: any) => {
    // 如果响应状态码为 401，则跳转到登录页面
    if (response.data.status === 405) {
      router.push('/login')
    }
    // 请求响应中的 config 的 url 会带上代理的 api，需要去掉
    response.config.url = response.config.url.replace('/api', '')
    // 请求完成，删除请求中的状态
    const key = getRequestKey(response.config);
    removePending(key);
    // 如果响应数据的 code 为 200，则返回响应
    if (response.data.code === 200) {
      return response
    }
    return response
  },
  (error: any) => {
    // 处理响应错误
    if (error && error.response) {
      switch (error.response.status) {
        case 405: // 未登录或登录过期，405：不允许在资源上使用请求方法
          // 如果响应状态码为 401，则跳转到登录页面
          router.push('/login')
          error.message = ' 登录过期，请重新登录'
          break;
        case 401:
          // 如果响应状态码为 405，则设置错误信息为 '请求错误'
          error.message = '请求错误'
      }
    }
    // 请求响应中的 config 的 url 会带上代理的 api，需要去掉
    error.config.url = error.config.url.replace('/api', '')
    // 请求完成，删除请求中的状态
    const key = getRequestKey(error.config);
    removePending(key);
    // 返回 Promise 拒绝
    return Promise.reject(error)
  }
)

export default service
