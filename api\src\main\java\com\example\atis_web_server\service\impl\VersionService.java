package com.example.atis_web_server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.atis_web_server.dto.query.PageDTO;
import com.example.atis_web_server.dto.req.ReqUpdateVersion;
import com.example.atis_web_server.mapper.VersionMapper;
import com.example.atis_web_server.pojo.VersionModel;
import com.example.atis_web_server.service.IVersionService;
import com.example.atis_web_server.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class VersionService extends ServiceImpl<VersionMapper, VersionModel> implements IVersionService {
    @Autowired
    private VersionMapper versionMapper;

    private static final Logger logger = LoggerFactory.getLogger(VersionService.class);

    @Override
    public List<VersionModel> getVersionList() {
        return versionMapper.selectList(null);
    }

    @Override
    public VersionModel toVersionModelByReq(ReqUpdateVersion req) {
        VersionModel versionModel = new VersionModel();
        versionModel.setVersion(req.getVersion());
        versionModel.setPlatform(req.getPlatform());
        versionModel.setId(req.getId());
        versionModel.setUrl(req.getUrl());
        versionModel.setUpdateTime(LocalDateTime.now());
        return versionModel;
    }

    @Override
    public boolean updateVersion(VersionModel versionModel) {
        return versionMapper.updateById(versionModel) > 0;
    }

    @Override
    public boolean isForcedUpdating(String version, String platform) {
        try {
            return !version.equals(getVersionByPlatform(platform));
        } catch (Exception e) {
            return true;
        }

    }

    private String getVersionByPlatform(String platform) {
        QueryWrapper<VersionModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(VersionModel::getPlatform, platform);
        VersionModel versionModel = versionMapper.selectOne(queryWrapper, false);
        return versionModel.getVersion();
    }

    @Override
    public String getURL(String platform) {
        String rs = getUtlByPlatform(platform);
        if (rs == null) {
            return getUtlByPlatform("Defeat");
        } else {
            return rs;
        }
    }

    private String getUtlByPlatform(String platform) {
        QueryWrapper<VersionModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(VersionModel::getPlatform, platform);
        VersionModel versionModel = versionMapper.selectOne(queryWrapper, false);
        return versionModel.getUrl();
    }

    @Override
    public PageDTO<VersionModel> queryPage() {
        // 1.构建分页条件
        Page<VersionModel> page = new Page<VersionModel>();

        // 2.分页查询
        Page<VersionModel> p = lambdaQuery().page(page);

        // 3. 封装VO结果
        return PageDTO.of(p, version -> {
            // 1.拷贝基础属性
            return BeanUtil.copyProperties(version, VersionModel.class);
        });
    }
}
