# 儿童孤独症AI干预管理分析系统 [Web服务端] V1.0 - 服务端设计说明书

## 1. 系统概述

### 1.1 项目背景
儿童孤独症AI干预管理分析系统是基于"儿童孤独症数字药物与系统研发"项目开发的综合性管理平台。系统主要围绕孤独症社交功能障碍等核心症状研究疾病的生理表征和内在机制，结合AI大语言模型（LLM）开发孤独症干预治疗系统，为疾病的客观量化诊断评估和干预治疗提供新的途径。

### 1.2 系统定位
本系统为Web服务端部分，包含后台管理系统和API服务端，主要负责：
- 用户（患者）信息管理
- 医生账户管理
- 任务池和任务记录管理
- 系统配置和版本管理
- 为移动端APP提供API接口服务

### 1.3 技术特点
- 采用前后端分离架构
- 基于Spring Boot微服务框架
- 集成AI大语言模型接口
- 支持语音识别和处理
- 实现任务自动化调度
- 提供完整的权限管理机制

## 2. 系统架构设计

### 2.1 总体架构
系统采用经典的三层架构模式：
```
┌─────────────────────────────────────────┐
│              前端展示层                    │
│        Vue.js + Element UI              │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              业务逻辑层                    │
│         Spring Boot + MyBatis           │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              数据持久层                    │
│              MySQL 8.0                 │
└─────────────────────────────────────────┘
```

### 2.2 模块架构
系统主要分为以下核心模块：

#### 2.2.1 Web后台管理模块
- **用户管理模块**：患者信息的增删改查、状态管理
- **医生管理模块**：医生账户管理、权限分配
- **任务池管理模块**：任务模板的创建、编辑、删除
- **任务记录管理模块**：任务执行记录的查看、分析

#### 2.2.2 API服务模块
- **用户认证模块**：登录验证、Token管理、会话控制
- **任务管理模块**：任务分发、进度跟踪、结果收集
- **配置管理模块**：系统参数配置、版本控制
- **数据统计模块**：用户行为分析、任务完成统计

## 3. 技术栈说明

### 3.1 后端技术栈
- **核心框架**：Spring Boot 2.7.6
- **数据访问**：MyBatis Plus 3.5.3.2
- **数据库**：MySQL 8.0
- **数据库迁移**：Flyway 7.15.0
- **安全认证**：JWT (JSON Web Token)
- **API文档**：Knife4j 4.1.0 (Swagger)
- **任务调度**：Spring Scheduling
- **数据验证**：Spring Boot Validation
- **工具库**：Hutool 5.8.25
- **HTTP客户端**：OkHttp 4.12.0
- **JSON处理**：FastJSON 1.2.83
- **邮件服务**：Spring Boot Mail

### 3.2 前端技术栈
- **核心框架**：Vue.js 2.6.14
- **UI组件库**：Element UI 2.15.3
- **状态管理**：Vuex 3.6.2
- **路由管理**：Vue Router 3.5.1
- **HTTP客户端**：Axios 1.7.7
- **开发语言**：TypeScript
- **构建工具**：Vue CLI
- **样式预处理**：支持SCSS/SASS

### 3.3 开发环境
- **Java版本**：JDK 17
- **Node.js版本**：支持ES2015+
- **包管理器**：Maven (后端) + NPM (前端)
- **开发端口**：后端8081，前端7000

## 4. 数据库设计

### 4.1 数据库概述
系统使用MySQL 8.0作为主数据库，采用InnoDB存储引擎，支持事务处理和外键约束。数据库字符集为utf8mb4，支持完整的Unicode字符集。

### 4.2 核心数据表设计

#### 4.2.1 用户表 (user)
```sql
CREATE TABLE `user` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL COMMENT '用户名(病历号)',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `name` varchar(255) NOT NULL COMMENT '姓名',
  `gender` bit(1) NOT NULL COMMENT '性别',
  `birthday` date NOT NULL COMMENT '生日',
  `day` int unsigned NOT NULL COMMENT '注册天数',
  `phone` varchar(255) DEFAULT NULL COMMENT '手机号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `login_time` datetime DEFAULT NULL,
  `doctor_id` bigint unsigned NOT NULL COMMENT '所属医生ID',
  `deleted` bit(1) DEFAULT NULL COMMENT '是否删除',
  `user_info` longtext NOT NULL COMMENT '用户信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username_UNIQUE` (`username`)
);
```

#### 4.2.2 医生表 (doctor)
```sql
CREATE TABLE `doctor` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(255) NOT NULL COMMENT '医生用户名',
  `password` varchar(255) NOT NULL COMMENT '登录密码',
  `name` varchar(255) NOT NULL COMMENT '医生姓名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `login_time` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username_UNIQUE` (`username`)
);
```

#### 4.2.3 任务池表 (task_pool)
```sql
CREATE TABLE `task_pool` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL COMMENT '任务名称',
  `type_id` smallint NOT NULL DEFAULT '2' COMMENT '任务类型',
  `prompt` text NOT NULL COMMENT '任务提示词',
  `doctor_id` bigint unsigned NOT NULL COMMENT '医生ID',
  `uid` bigint unsigned NOT NULL COMMENT '用户ID',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `task_record_id` int NOT NULL COMMENT '任务记录ID',
  `used` bit(1) DEFAULT b'0' COMMENT '是否已使用',
  `deleted` bit(1) DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
);
```

#### 4.2.4 任务记录表 (task)
```sql
CREATE TABLE `task` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tid` bigint unsigned NOT NULL COMMENT '任务ID',
  `uid` bigint unsigned NOT NULL COMMENT '用户ID',
  `doctor_id` bigint unsigned NOT NULL COMMENT '医生ID',
  `day` int unsigned NOT NULL COMMENT '第几天任务',
  `time` int NOT NULL DEFAULT '300' COMMENT '任务时长',
  `completion` bit(1) DEFAULT b'0' COMMENT '是否完成',
  `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
  `chat_history` text COMMENT '聊天记录',
  `score` int DEFAULT '0' COMMENT '任务得分',
  `used` bit(1) DEFAULT b'0' COMMENT '是否已使用',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `chat_summary` varchar(255) DEFAULT NULL COMMENT '聊天总结',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 4.3 数据表关系
- user表与doctor表：多对一关系，一个医生可以管理多个患者
- task_pool表与user表：多对一关系，一个用户可以有多个任务
- task表与task_pool表：多对一关系，任务记录来源于任务池
- user_sessions表：用户会话管理，实现单点登录

## 5. 核心功能模块设计

### 5.1 用户管理模块
**功能描述**：管理系统中的患者用户信息
**主要功能**：
- 用户注册：支持批量导入和单个添加
- 用户信息维护：基本信息修改、状态管理
- 用户查询：支持多条件组合查询和分页
- 用户状态管理：启用/禁用账户

**核心类**：
- `UserController`：用户相关API接口
- `UserService`：用户业务逻辑处理
- `UserModel`：用户实体类

### 5.2 医生管理模块
**功能描述**：管理系统中的医生账户
**主要功能**：
- 医生登录认证
- 医生信息管理
- 权限控制

**核心类**：
- `BgDoctorController`：医生管理API接口
- `DoctorService`：医生业务逻辑
- `DoctorModel`：医生实体类

### 5.3 任务管理模块
**功能描述**：管理孤独症干预任务的创建、分发和执行
**主要功能**：
- 任务池管理：任务模板的增删改查
- 任务分发：自动为用户生成每日任务
- 任务跟踪：记录任务执行过程和结果
- 任务统计：分析任务完成情况

**核心类**：
- `TaskPoolController`：任务池管理
- `TaskRecordController`：任务记录管理
- `TaskRecordService`：任务业务逻辑
- `ScheduledTask`：定时任务调度

### 5.4 系统配置模块
**功能描述**：管理系统运行参数和配置
**主要功能**：
- AI模型配置：GPT API密钥和地址
- 语音服务配置：Azure语音服务参数
- 系统参数配置：录音时长、网络重试次数等
- 版本管理：客户端版本控制和更新

**核心类**：
- `ConfigController`：配置管理API
- `ConfigService`：配置业务逻辑
- `ConfigModel`：配置实体类

## 6. 安全机制设计

### 6.1 认证机制
系统采用JWT（JSON Web Token）进行用户认证：
- 用户登录成功后生成JWT Token
- Token包含用户ID、过期时间等信息
- 所有API请求需携带有效Token

### 6.2 会话管理
实现单点登录机制：
- 用户登录时创建会话记录
- 同一用户在其他设备登录时，原会话失效
- 定期清理过期会话

### 6.3 权限控制
- 基于角色的访问控制（RBAC）
- 医生只能管理自己的患者
- 前端路由守卫验证用户权限

### 6.4 数据安全
- 密码使用SHA-256加密存储
- 敏感配置信息加密保存
- SQL注入防护
- XSS攻击防护

### 6.5 接口安全
- Token过滤器验证请求合法性
- 请求频率限制
- 参数验证和异常处理
- 日志记录和监控

## 7. 系统特色功能

### 7.1 AI集成
- 集成GPT大语言模型API
- 支持自定义提示词模板
- 智能对话任务生成

### 7.2 语音处理
- 集成Azure语音服务
- 支持语音识别和合成
- 可配置语音参数

### 7.3 任务调度
- 每日自动生成用户任务
- 定时更新用户注册天数
- 灵活的任务分配策略

## 8. 部署架构设计

### 8.1 部署环境规划

#### 8.1.1 开发环境
- **服务器配置**：2核CPU，4GB内存，50GB存储
- **数据库**：MySQL 8.0单机部署
- **应用服务器**：内置Tomcat，端口8081
- **前端服务**：Vue开发服务器，端口7000
- **域名配置**：localhost或内网IP访问

#### 8.1.2 测试环境
- **服务器配置**：8核CPU，16GB内存，500GB存储
- **数据库**：MySQL主从复制配置
- **应用服务器**：Docker容器化部署
- **负载均衡**：Nginx反向代理
- **域名配置**：测试域名HTTPS访问

#### 8.1.3 生产环境
- **服务器配置**：8核CPU，16GB内存，500GB存储
- **数据库**：MySQL主从复制配置
- **应用服务器**：Docker容器化部署
- **负载均衡**：Nginx反向代理
- **域名配置**：正式域名HTTPS访问

### 8.2 容器化部署

#### 8.2.1 Docker配置
```dockerfile
FROM openjdk:17-jdk-slim
VOLUME /tmp
COPY target/atis_web_server-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java","-jar","/app.jar"]
```

#### 8.2.2 Docker Compose配置
```yaml
version: '3.8'
services:
  atis-app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=production
    depends_on:
      - mysql

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: atis
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
```

## 9. 性能优化设计

### 9.1 数据库优化

#### 9.1.1 查询优化
- **分页查询优化**：使用LIMIT和OFFSET优化
- **连接查询优化**：合理使用JOIN减少查询次数
- **条件查询优化**：WHERE条件使用索引字段
- **排序优化**：ORDER BY使用索引字段

## 10. 监控和日志系统

### 10.1 应用监控

#### 10.1.1 健康检查端点
```java
@RestController
public class HealthController {
    @ApiOperation("测试")
    @GetMapping("/client/test")
    public Result test() {
        logger.info("***** test *****");
        Map<String, Object> map = new HashMap<>();
        map.put("test", "测试成功 2");
        return Result.success(map);
    }
}
```

### 10.2 日志系统

#### 10.2.1 日志配置
```xml
<configuration>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/atis-server.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/atis-server.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <root level="INFO">
        <appender-ref ref="FILE" />
    </root>
</configuration>
```

## 11. 数据迁移和备份

### 11.1 数据迁移策略

#### 11.1.1 Flyway数据库迁移
```sql
-- V1.0.2__add_user_info_column.sql
ALTER TABLE user ADD COLUMN user_info LONGTEXT NOT NULL DEFAULT '';

-- V1.0.3__create_user_sessions_table.sql
CREATE TABLE user_sessions (
    id BIGINT NOT NULL AUTO_INCREMENT,
    user_id VARCHAR(255) NOT NULL,
    token VARCHAR(512) NOT NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY user_id (user_id)
);
```

#### 11.1.2 数据版本控制
- **版本号管理**：使用语义化版本号
- **迁移脚本**：每个版本对应的数据库变更脚本
- **回滚策略**：支持数据库结构回滚
- **数据验证**：迁移后数据完整性验证

#### 11.2.2 应用备份
- **代码备份**：Git版本控制和远程仓库
- **配置备份**：配置文件定期备份
- **日志备份**：重要日志文件归档
- **文件备份**：上传文件和静态资源备份

## 12. 安全加固措施

### 12.1 应用安全

#### 12.1.1 输入验证
```java
@PostMapping("/users/register")
public Result registerUser(@Valid @RequestBody ReqRegisterUser req) {
    // 参数验证
    if (!ValidationUtils.isValidUsername(req.getUsername())) {
        return Result.error(400, "用户名格式不正确");
    }

    // SQL注入防护
    String username = StringEscapeUtils.escapeSql(req.getUsername());

    // XSS防护
    String name = StringEscapeUtils.escapeHtml4(req.getName());

    return userService.registerUser(req);
}
```

#### 12.12 权限控制
```java
@PreAuthorize("hasRole('DOCTOR') and @userService.canAccessUser(#uid, authentication.name)")
public Result getUserDetail(@PathVariable Long uid) {
    return Result.success(userService.getUserDetail(uid));
}
```

## 13. 扩展性设计

### 13.1 微服务架构预留

#### 13.1.1 服务拆分规划
- **用户服务**：用户管理相关功能
- **任务服务**：任务管理和执行功能
- **AI服务**：AI模型调用和处理
- **通知服务**：消息推送和通知功能
- **文件服务**：文件上传和存储功能

#### 13.1.2 服务间通信
- **REST API**：同步调用使用HTTP REST
- **消息队列**：异步处理使用RabbitMQ
- **服务发现**：使用Eureka或Consul
- **配置中心**：使用Spring Cloud Config

本设计说明书详细描述了儿童孤独症AI干预管理分析系统Web服务端的技术架构、功能模块和安全机制，为系统的开发、部署和维护提供了完整的技术指导。
