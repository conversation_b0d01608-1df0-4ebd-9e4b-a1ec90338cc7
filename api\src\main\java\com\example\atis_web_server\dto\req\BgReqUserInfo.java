package com.example.atis_web_server.dto.req;


import com.example.atis_web_server.dto.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "用户查询条件实体")
public class BgReqUserInfo extends PageQuery {
    @ApiModelProperty("用户名")
    private String username = null;
    @ApiModelProperty("姓名")
    private String name = null;
    @ApiModelProperty("年龄")
    private Integer age = 0;
    @ApiModelProperty("医生姓名")
    private String doctorName;  // admin
    @ApiModelProperty("注册天数")
    private Integer day = 0;
    @ApiModelProperty("是否逻辑删除")
    private Boolean deleted = false;
}
