package com.example.atis_web_server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.example.atis_web_server.common.ChatGpt;
import com.example.atis_web_server.dto.query.PageDTO;
import com.example.atis_web_server.dto.req.ReqTaskRecordInfo;
import com.example.atis_web_server.dto.req.ReqUpdateTaskRecord;
import com.example.atis_web_server.dto.resp.RespTaskInfo;
import com.example.atis_web_server.dto.resp.RespTaskRecordInfo;
import com.example.atis_web_server.mapper.TaskPoolMapper;
import com.example.atis_web_server.mapper.TaskRecordMapper;
import com.example.atis_web_server.mapper.UserMapper;
import com.example.atis_web_server.pojo.TaskPoolModel;
import com.example.atis_web_server.pojo.TaskRecordModel;
import com.example.atis_web_server.pojo.UserDoctorModel;
import com.example.atis_web_server.pojo.UserModel;
import com.example.atis_web_server.service.ITaskRecordService;
import com.example.atis_web_server.utils.Utils;
import okhttp3.RequestBody;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TaskRecordService extends ServiceImpl<TaskRecordMapper, TaskRecordModel> implements ITaskRecordService {
    @Autowired
    private TaskRecordMapper taskRecordMapper;
    @Autowired
    private TaskPoolService taskPoolService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private TaskPoolMapper taskPoolMapper;
    @Autowired
    private MailService mailService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private ResourceLoader resourceLoader;
    private String summaryPrompt;

    @Value("${app.taskNumPerDay}")
    private int taskNumPerDay;

    private static final Logger logger = LoggerFactory.getLogger(TaskRecordService.class);

    public TaskRecordService(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    @PostConstruct
    public void init() throws Exception {
        // 在这里执行你的任务
        logger.info("任务记录服务已初始化，执行启动时任务...");
        logger.info("taskNumPerDay: {}", taskNumPerDay);

        // 加载提示词
        Resource resource = resourceLoader.getResource("classpath:summaryPrompt.md");
        summaryPrompt = new String(resource.getInputStream().readAllBytes());

        // 检测所有用户任务池中数量是否足够
        checkTaskNumForAllUsers();
    }

    /**
     * 添加任务
     */
    @Override
    public void add(Integer day, TaskPoolModel taskPoolModel) throws Exception {
        TaskRecordModel taskRecordModel = new TaskRecordModel();
        taskRecordModel.setTid(taskPoolModel.getId());
        taskRecordModel.setUid(taskPoolModel.getUid());
        taskRecordModel.setDoctorId(taskPoolModel.getDoctorId());
        taskRecordModel.setRemark(taskPoolModel.getRemark());
        taskRecordModel.setDay(day);
        taskRecordModel.setTime(configService.getInfo().getChatTime());
        save(taskRecordModel);
    }

    /**
     * 查询任务: 根据各种条件查询任务 分页 复杂
     *  TODO 是否有更好的联表查询？
     */
    @Override
    public PageDTO<RespTaskRecordInfo> queryTaskRecordPage(ReqTaskRecordInfo req, Long doctorId) {
        String taskName = req.getName();
        String username = req.getUsername();
        Boolean completion = req.getCompletion();
        LocalDateTime completeTime = req.getCompleteTime();
        Integer day = req.getDay();
        Boolean used = req.getUsed();
        String doctorName = req.getDoctorName();

        // 1.构建分页条件
        Page<TaskRecordModel> page = req.toMpPage();
        // 2.分页查询
        // 2.1 先写为查两次的方法
        LambdaQueryWrapper<UserModel> wrapper = new LambdaQueryWrapper<UserModel>()
                .select(UserModel::getId, UserModel::getUsername)
                .eq(doctorId != null, UserModel::getDoctorId, doctorId)
                .like(username != null, UserModel::getUsername, username);
        List<UserModel> users = userMapper.selectList(wrapper);
        if (users.isEmpty()) {
            return new PageDTO<>();
        }
        List<Long> uidList = users.stream().map(UserModel::getId).collect(Collectors.toList());

        LambdaQueryWrapper<TaskPoolModel> taskPoolWrapper = new LambdaQueryWrapper<TaskPoolModel>()
                .select(TaskPoolModel::getId)
                .like(taskName != null, TaskPoolModel::getName, taskName);
        List<TaskPoolModel> tasks = taskPoolMapper.selectList(taskPoolWrapper);
        if (tasks.isEmpty()) {
            return new PageDTO<>();
        }
        List<Long> taskIdList = tasks.stream().map(TaskPoolModel::getId).collect(Collectors.toList());


        Page<TaskRecordModel> p = lambdaQuery()
                .eq(doctorId != null, TaskRecordModel::getDoctorId, doctorId)  // TODO admin 时，这个限制怎么办
                .in(taskName != null, TaskRecordModel::getTid, taskIdList)  // TODO 查了两次
                .in(username != null, TaskRecordModel::getUid, uidList)  // TODO admin 时，这个限制怎么办 这里如何查？
                .eq(completion != null, TaskRecordModel::getCompletion, completion)
                .ge(completeTime != null, TaskRecordModel::getCompleteTime, completeTime)
                .ge(day != null, TaskRecordModel::getDay, day)
                .eq(used != null, TaskRecordModel::getUsed, used)
//                .like(doctorName != null, TaskRecordModel::getDoctorName??, doctorNameReq)  //  TODO admin 这里如何查？
                .page(page);


        // 3. 查询用户
        List<Long> userIds = p.getRecords().stream().map(TaskRecordModel::getUid).collect(Collectors.toList());
        if (userIds.isEmpty()) {
            return new PageDTO<>();
        }
        // 3.2.根据用户id查询用户表
        List<UserModel> userModels = Db.lambdaQuery(UserModel.class).in(UserModel::getId, userIds).list();

        // 3.3 查询任务池
        List<Long> taskIds = p.getRecords().stream().map(TaskRecordModel::getTid).collect(Collectors.toList());
        if (taskIds.isEmpty()) {
            return new PageDTO<>();
        }
        // 3.4.根据任务id查询任务表
        List<TaskPoolModel> taskPoolModels = Db.lambdaQuery(TaskPoolModel.class).in(TaskPoolModel::getId, taskIds).list();


        // 4.封装VO结果
        return PageDTO.of(p, taskRecord -> {
            // 1.拷贝基础属性
            RespTaskRecordInfo vo = BeanUtil.copyProperties(taskRecord, RespTaskRecordInfo.class);
            // 2.1设置用户名  // TODO 这么查，会不会有错误
            userModels.stream()
                    .filter(udm -> udm.getId().equals(taskRecord.getUid()))
                    .findFirst().ifPresent(userModel -> vo.setUsername(userModel.getUsername()));

            // 2.2设置用任务名  // TODO 这么查，会不会有错误
            taskPoolModels.stream()
                    .filter(udm -> udm.getId().equals(taskRecord.getTid()))
                    .findFirst().ifPresent(taskPoolModel -> vo.setName(taskPoolModel.getName()));

            return vo;
        });

    }

    /**
     * 查询任务: 根据 taskRecordId 返回历史聊天记录 （供下载和查看）
     * TODO ： 批量查询功能，有需求了再做
     */
    public String getChatHistory(Long taskRecordId, Long doctorId) {
        return getById(taskRecordId).getChatHistory();
    }

    /**
     * 构建 TaskPoolModel
     *
     * @return TaskPoolModel
     */
    @Override
    public TaskRecordModel toTaskRecordModel(ReqUpdateTaskRecord req) {
        return BeanUtil.copyProperties(req, TaskRecordModel.class);
    }


    /**
     * 选择下发的任务 根据user_id 筛选day 当天的没有完成的一个任务
     *
     * @return TaskRecordModel
     */
    @Override
    public RespTaskInfo getByDay(Long userId) throws Exception {
        int day = userService.getById(userId).getDay();
        if (checkTaskNumByCompletion(userId, day)) {
            return null;
        }
        TaskRecordModel taskRecordModel = getTaskByDay(day, userId);
        // null :今日任务已全部完成
        return getRespTaskInfo(taskRecordModel);
    }

    /*
    从任务池，随机选取一个未被使用的任务，添加到任务记录表中。
    然后将此任务记录返回
     */
    private TaskRecordModel getTaskByDay(int day, Long userId) throws Exception {
        List<TaskPoolModel> taskPoolModelList = taskPoolService.selectByUsed(userId, false);
        if (taskPoolModelList.isEmpty()) {
            throw new Exception("No available tasks in the task pool for user: " + userId);
        }
        TaskPoolModel taskPoolModel = Utils.getRandomElement(taskPoolModelList);
        add(day, taskPoolModel);
        return getNewTask(userId, day);
    }

    private TaskRecordModel getNewTask(Long uid, int day) throws Exception {
        LambdaQueryWrapper<TaskRecordModel> queryWrapper = Wrappers.<TaskRecordModel>lambdaQuery()
                .eq(TaskRecordModel::getUid, uid)
                .eq(TaskRecordModel::getDay, day)
                .eq(TaskRecordModel::getCompletion, false)
                .eq(TaskRecordModel::getUsed, false);
        List<TaskRecordModel> taskRecordModelList = taskRecordMapper.selectList(queryWrapper);

        //  第一天上线时，由于版本差异，当天任务已生成，此处可能会有多个待完成的任务。
        //  因此，此处暂时只取最后一个任务。
        // TODO： 此代码可以第二天之后再上线
//        assert taskRecordModelList.size() == 1;
        // TODO: 不过，可能也有需求为：允许有多个待完成任务，然后默认选取最后一个。
        //  比如，如果可以提前生成一个任务，然后认为当前任务不合适，再次生成一个任务。

        return taskRecordModelList.get(taskRecordModelList.size() - 1);
    }

    private boolean checkTaskNumByCompletion(Long uid, int day) {
        LambdaQueryWrapper<TaskRecordModel> queryWrapper = Wrappers.<TaskRecordModel>lambdaQuery()
                .eq(TaskRecordModel::getUid, uid)
                .eq(TaskRecordModel::getDay, day)
                .eq(TaskRecordModel::getCompletion, true);
        List<TaskRecordModel> taskRecordModelList = taskRecordMapper.selectList(queryWrapper);
        return taskRecordModelList.size() >= taskNumPerDay;
    }

    private Long fetchDoctorId(Long userId) throws Exception {
        List<UserDoctorModel> userDoctors = Db.lambdaQuery(UserDoctorModel.class)
                .eq(UserDoctorModel::getUid, userId)
                .list();
        if (userDoctors.isEmpty()) {
            throw new Exception("No doctor found for user ID: " + userId);
        }
        return userDoctors.get(0).getDoctorId();
    }

    private RespTaskInfo getRespTaskInfo(TaskRecordModel taskRecordModel) {
        TaskPoolModel taskPoolModel = taskPoolMapper.selectById(taskRecordModel.getTid());
        RespTaskInfo respTaskInfo = new RespTaskInfo();
        respTaskInfo.setId(taskRecordModel.getId());
        respTaskInfo.setTid(taskRecordModel.getTid());
        respTaskInfo.setName(taskPoolModel.getName());
        respTaskInfo.setDay(taskRecordModel.getDay());
        respTaskInfo.setTime(taskRecordModel.getTime());
        respTaskInfo.setTypeId(taskPoolModel.getTypeId());
        respTaskInfo.setPrompt(taskPoolModel.getPrompt());
        respTaskInfo.setGptName("gpt-4o-mini");
        return respTaskInfo;
    }

    /**
     * 根据tid设置任务已经完成
     *
     * @param id 任务记录id
     */
    @Override
    public void updateCompletedById(Long id) {
        TaskRecordModel taskRecordModel = getById(id);
        //1.1 设置任务已完成
        taskRecordModel.setCompletion(true);
        //1.2 设置任务完成的时间为现在的时间
        LocalDateTime now = LocalDateTime.now();
        taskRecordModel.setCompleteTime(now);
        saveOrUpdate(taskRecordModel);
        taskRecordMapper.updateById(taskRecordModel);

        logger.info("*** 任务完成时间：" + now);

        // 2. 将该用户任务池中对应任务的used设为true
        taskPoolService.updateUsedById(taskRecordModel.getTid());
    }

    /**
     * 根据tid 保存历史记录
     *
     * @param id id
     */
    @Override
    public void updateChatHistoryById(Integer id, String chatHistory, int uid, boolean exception) throws IOException {
        //保存聊天记录及对应的总结
        TaskRecordModel taskRecordModel = getById(id);
        taskRecordModel.setChatHistory(chatHistory);

        // 如果任务异常，则将任务标记为未完成、已使用。
        taskRecordModel.setCompletion(!exception);
        taskRecordModel.setUsed(exception);
        LocalDateTime now = LocalDateTime.now();
        taskRecordModel.setCompleteTime(now);

        if (!exception) {
            JSONObject json = JSONObject.parseObject(chatHistory);

            RequestBody body = ChatGpt.buildChatRequestBody(
                    summaryPrompt,  // 系统角色设定
                    json.getString("TextHistory")  // 用户问题
            );
            String summaryData = ChatGpt.PostMsg(body);
            String userInfo = "";

            if (summaryData != null) {
                summaryData = summaryData.replace("```json", "").replace("```", "");
                JSONObject jsonResponse = JSONObject.parseObject(summaryData);
                userInfo = jsonResponse.getString("UserInfo");
            }

            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String date = dateFormatter.format(LocalDateTime.now());
            taskRecordModel.setChatSummary(String.format("[%s] ", date) + userInfo);  // summaryData
        }

        updateById(taskRecordModel);

        // TODO： 用户个性化信息处理算法未完成前，不更新用户信息【目前会通过UserInfo发送到客户端，但是客户端不会并入初始提示词中】
//        // 任务异常时，此任务所总结的个性化信息，不会放入用户信息中
//        if (!exception) {
//            // TODO: 已经修改了 summaryData 的数据格式和内容，此处需要添加算法来处理
//            // 1. 循环该用户 **最近100次** 的已完成任务的  ChatSummary
//            // 2. 然后发送给AI处理，让其返回，将返回的数据作为最新的 userInfo
//
//            // 将这份聊天总结合并到用户个人信息中
//            userService.updateUserInfoById(uid, userInfo);
//        }
    }

    @Override
    public boolean isExistsTaskRecordById(Long Id) {
        return !ObjUtil.isEmpty(taskRecordMapper.selectById(Id));
    }

    /**
     * getScheduleByUid
     *
     * @param uid uid
     */
    @Override
    public float getScheduleByUid(Long uid) {
        int day = userService.getById(uid).getDay();
        int completeNum = lambdaQuery()
                .eq(uid != 0, TaskRecordModel::getUid, uid)
                .eq(day != 0, TaskRecordModel::getDay, day)
                .eq(TaskRecordModel::getCompletion, true)
                .list().size();

        BigDecimal divisor = new BigDecimal(completeNum); // 除数
        return divisor.divide(BigDecimal.valueOf(taskNumPerDay), 2, RoundingMode.HALF_UP).floatValue();
    }

    public void checkTaskNumForAllUsers() throws Exception {
        for (UserModel userModel : userMapper.selectList(null)) {
            Long uid = userModel.getId();
            Integer day = userModel.getDay();
            Long doctorId = userModel.getDoctorId();
            boolean result = taskPoolService.selectByUsed(uid, false).size() >= taskNumPerDay;
            if (!result) {
                // 从任务记录生成任务
                taskPoolService.generateTaskFromAI(uid, doctorId);
            }
        }
    }

    /*
    用户每日完成任务情况总结
    1. 定时任务 TODO：后期加上对于8点时服务中断的处理
    2. 邮箱加入数据库（最好有个邮箱表）
     */
    @Override
    public void getYesterdayTaskData() {
        logger.trace("getYesterdayTaskData");
        String contentTableHead = "<html>\n<body>\n<table border=1>\n";
        String tableHead = "<tr>" +
                "\n<td>序号</td>" +
                "\n<td>用户ID</td>" +
                "\n<td>姓名</td>" +
                "\n<td>完成任务数</td>" +
                "\n</tr>\n";
        StringBuilder tableBody = new StringBuilder();
        List<Map<String, Object>> todayTaskData = taskRecordMapper.getYesterdayTaskData(1L);  // doctor_id=1 生产环境 李海峰医生

        // 打印数据
        todayTaskData.forEach(data -> {
            logger.debug("Name: {}, Count: {}", data.get("name"), data.get("count"));
        });

        todayTaskData.stream()
                .sorted((a, b) -> Long.compare(
                        (long) b.get("count"),  // 降序排列，所以用 b 和 a 比较
                        (long) a.get("count")
                ))
                .forEach(item -> {
                    tableBody.append("<tr>\n")
                            .append("<td>").append(todayTaskData.indexOf(item) + 1).append("</td>\n")
                            .append("<td>").append(item.get("id")).append("</td>\n")
                            .append("<td>").append(item.get("name")).append("</td>\n")
                            .append("<td>").append(item.get("count")).append("</td>\n")
                            .append("</tr>\n");
                });
        String contentTableEnd = "</table>\n</body>\n</html>";
//        mailService.sendHtmlMail("<EMAIL>", "Task Record(" + LocalDate.now().minusDays(1) + ")",
//                contentTableHead + tableHead + tableBody + contentTableEnd);
//        mailService.sendHtmlMail("<EMAIL>", "Task Record(" + LocalDate.now().minusDays(1) + ")",
//                contentTableHead + tableHead + tableBody + contentTableEnd);
        mailService.sendHtmlMail("<EMAIL>", "Task Record(" + LocalDate.now().minusDays(1) + ")",
                contentTableHead + tableHead + tableBody + contentTableEnd);
    }
}
