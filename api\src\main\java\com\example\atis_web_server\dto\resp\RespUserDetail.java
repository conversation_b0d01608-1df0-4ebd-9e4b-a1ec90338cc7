package com.example.atis_web_server.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "用户信息查询请求实体-客户端")
public class RespUserDetail {
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("用户ID")
    private Long uid;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("性别")
    private Boolean gender;
    @ApiModelProperty("年龄")
    private Integer age;
    @ApiModelProperty("token")
    private String token;
    @ApiModelProperty("star:未实现")
    private Integer star;
    @ApiModelProperty("个人信息：由聊天总结合并而成")
    private String userInfo;
}
