# 儿童孤独症AI干预管理分析系统 [Web服务端] V1.0 - API接口文档

# 1. 接口概述

## 1.1 接口说明
本文档描述了儿童孤独症AI干预管理分析系统Web服务端提供的所有API接口，包括用户管理、医生管理、任务管理、系统配置等功能模块的接口规范。

## 1.2 接口分类
系统API接口主要分为以下几类：
- **客户端接口** (`/client/*`)：为移动端APP提供的接口
- **后台管理接口** (`/Background/*`)：为Web后台管理系统提供的接口
- **公共接口**：版本检查、系统测试等接口

## 1.3 基础信息
- **协议**：HTTP/HTTPS
- **数据格式**：JSON
- **字符编码**：UTF-8
- **开发环境地址**：http://localhost:8081
- **生产环境地址**：https://atis.zjbci.com

# 2. 认证授权机制

## 2.1 Token认证
系统采用JWT（JSON Web Token）进行接口认证：

**Token获取**：
- 用户通过登录接口获取Token
- Token有效期为24小时
- Token包含用户ID、角色等信息

**Token使用**：
- 在请求头中添加：`Authorization: Bearer {token}`
- 系统自动验证Token有效性
- Token过期需重新登录获取

## 2.2 会话管理
- 实现单点登录，同一用户只能在一个设备上保持登录状态
- 新设备登录会使原设备Token失效
- 支持主动登出和Token刷新

# 3. 统一响应格式

## 3.1 成功响应
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    // 具体数据内容
  }
}
```

## 3.2 错误响应
```json
{
  "code": 错误码,
  "msg": "错误描述",
  "data": null
}
```

## 3.3 常用状态码
- **200**：请求成功
- **401**：未授权，Token无效或过期
- **404**：资源不存在
- **423**：密码错误
- **500**：服务器内部错误

# 4. 错误码说明

## 4.1 通用错误码
- **200**：操作成功
- **400**：请求参数错误
- **401**：未授权，需要登录
- **403**：权限不足
- **404**：资源不存在
- **500**：服务器内部错误

## 4.2 业务错误码
- **423**：密码错误
- **424**：用户已存在
- **425**：用户不存在
- **426**：任务不存在
- **427**：任务已完成
- **428**：权限不足

# 客户端用户管理接口

## POST 查询用户信息

POST /client/UserInfo/GetUser

查询用户 根据username

> Body 请求参数

```json
{
  "uid": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUserInfo](#schemarequserinfo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 更改用户密码

POST /client/UserInfo/ModifyPassword

> Body 请求参数

```json
{
  "password": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |none|
|body|body|[ReqUpdateUserPassword](#schemarequpdateuserpassword)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 登录 用户

POST /client/Login

> Body 请求参数

```json
{
  "username": "string",
  "password": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqLogin](#schemareqlogin)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 记录登录数据，并获取配置参数

POST /client/UserInfo/recordLogin

> Body 请求参数

```json
{
  "id": 0,
  "uid": 0,
  "loginTime": "string",
  "place": "string",
  "device": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |none|
|body|body|[UserLoginRecordModel](#schemauserloginrecordmodel)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# BgLogController

## GET getLogs

GET /logs

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|date|query|string| 是 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
[
  ""
]
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|Inline|

### 返回数据结构

# 后台用户管理接口

## POST 注册 用户

POST /Background/users/register

新增用户 TODO 优化重复代码

> Body 请求参数

```json
{
  "username": "string",
  "name": "string",
  "gender": true,
  "birthday": "string",
  "phone": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |none|
|body|body|[ReqRegisterUser](#schemareqregisteruser)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 查询单个用户 - 后台

POST /Background/users/info

查询单个用户 主要是更新用户时，需要返回单个用户的部分信息

> Body 请求参数

```json
{
  "uid": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUserInfo](#schemarequserinfo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 修改单个用户

POST /Background/users/update

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "gender": true,
  "birthday": "string",
  "phone": "string",
  "doctor_id": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUpdateUser](#schemarequpdateuser)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 重置单个用户密码

POST /Background/users/resetPwd

> Body 请求参数

```json
{
  "uid": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUserInfo](#schemarequserinfo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## GET 根据复杂条件分页查询用户接口

GET /Background/users/queryPage

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 否 |页码|
|pageSize|query|integer| 否 |一页的数量|
|orderItems[0].column|query|string| 否 |none|
|orderItems[0].asc|query|boolean| 否 |none|
|username|query|string| 否 |用户名|
|name|query|string| 否 |姓名|
|age|query|integer| 否 |年龄|
|doctorName|query|string| 否 |admin|
|day|query|integer| 否 |注册天数|
|deleted|query|boolean| 否 |是否逻辑删除|
|Authorization|header|string| 是 |none|

#### 详细说明

**doctorName**: admin
医生姓名

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 杂项管理接口

## POST 版本检查

POST /client/update

> Body 请求参数

```json
{
  "version": "string",
  "platform": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUpdatingInfo](#schemarequpdatinginfo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## GET 测试

GET /client/test

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## GET /

GET /

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 客户端问卷管理接口

## POST 保存问卷记录

POST /client/QuestionnaireUrl

保存问卷结果

> Body 请求参数

```json
{
  "key": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |String|
|body|body|[MapString](#schemamapstring)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 检查当天是否做了问卷

POST /client/IsQuestionnaire

查询当天是否做了问卷

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 后台资源更新管理接口

## GET 获取 资源更新 的信息

GET /Background/AssetsUpdating/getAssetsInfo

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": null,
  "msg": null,
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|null|false|none||响应码，1 代表成功; 0 代表失败|
|» msg|null|false|none||响应信息 描述字符串|
|» data|null|false|none||返回的数据|

## POST 更新 资源更新 的信息

POST /Background/AssetsUpdating/updateAssetsInfo

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|query|integer(int64)| 是 |none|
|active|query|boolean| 否 |none|
|url|query|string| 否 |none|
|update_time|query|string| 否 |none|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 后台医生管理接口

## POST 医生 注册

POST /Background/doctors/register

新增医生数据

> Body 请求参数

```json
{
  "username": "string",
  "password": "string",
  "name": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqRegisterDoctor](#schemareqregisterdoctor)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 医生 登录

POST /Background/doctors/login

> Body 请求参数

```json
{
  "username": "string",
  "password": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqLogin](#schemareqlogin)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 根据Id获取医生信息

POST /Background/doctors/info/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer| 是 |用户id|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 更新单个医生信息

POST /Background/doctors/update

> Body 请求参数

```json
{
  "id": 0,
  "username": "string",
  "name": "string",
  "create_time": "string",
  "deleted": true,
  "login_time": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUpdateDoctor](#schemarequpdatedoctor)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 后台任务池管理接口

## POST 修改单个任务池中的任务

POST /Background/TaskPool/update

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "prompt": "string",
  "used": true,
  "remark": "string",
  "deleted": false
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUpdateTaskPool](#schemarequpdatetaskpool)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## GET 根据复杂条件分页查询任务池接口

GET /Background/TaskPool/queryPage

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 否 |页码|
|pageSize|query|integer| 否 |一页的数量|
|orderItems[0].column|query|string| 否 |none|
|orderItems[0].asc|query|boolean| 否 |none|
|name|query|string| 否 |任务名|
|username|query|string| 否 |患者用户名:目前未设置如何查询|
|typeId|query|integer| 否 |任务类型：0命名naming，1提要求answer，2对话任务chat|
|used|query|boolean| 否 |任务是否被完成过：对应的任务记录中的任务是否有被完成过的|
|deleted|query|boolean| 否 |是否逻辑删除|
|taskRecordId|query|integer| 否 |由哪个任务记录对应的聊天记录生成的：-1为默认任务|
|doctorName|query|string| 否 |admin|
|Authorization|header|string| 是 |none|

#### 详细说明

**doctorName**: admin
医生姓名

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 查询单个任务 - 后台

POST /Background/TaskPool/info

查询单个任务 主要是更新任务时，需要返回单个任务的部分信息

> Body 请求参数

```json
{
  "uid": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUserInfo](#schemarequserinfo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 更新任务池的默认任务 - 后台

POST /Background/TaskPool/updateDefaultTask

更新任务池的默认任务

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 后台版本管理接口

## GET getVersionList

GET /Background/Version/getVersionList

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 更新版本信息

POST /Background/Version/updateVersion

> Body 请求参数

```json
{
  "id": 0,
  "platform": "string",
  "version": "string",
  "url": "string"
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUpdateVersion](#schemarequpdateversion)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 客户端日志上传管理接口

## POST 上传日志

POST /client/UpdateLog

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 客户端任务记录管理接口

## POST 用户获取当天任务

POST /client/GetTask

下发任务

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 标记任务完成

POST /client/TaskIsComplete

任务完成

> Body 请求参数

```json
{
  "key": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[MapLong](#schemamaplong)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 添加聊天记录

POST /client/UploadChatHistory

上传聊天记录

> Body 请求参数

```json
{
  "key": {}
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[MapObject](#schemamapobject)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 上传EEG

POST /client/UploadEEG

上传EEG

> Body 请求参数

```yaml
file: string

```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |none|
|body|body|object| 否 |none|
|» file|body|string(binary)| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 返回任务进度

POST /client/GetTaskScheduleUrl

返回任务进度

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 后台任务记录管理接口

## POST 修改单个任务池中的任务

POST /Background/TaskRecord/update

> Body 请求参数

```json
{
  "id": 0,
  "completion": true,
  "score": 0,
  "remark": "string",
  "time": 0,
  "used": true
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUpdateTaskRecord](#schemarequpdatetaskrecord)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": null,
  "msg": null,
  "data": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|null|false|none||响应码，1 代表成功; 0 代表失败|
|» msg|null|false|none||响应信息 描述字符串|
|» data|null|false|none||返回的数据|

## GET 根据复杂条件分页查询任务池接口

GET /Background/TaskRecord/queryPage

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|pageNo|query|integer| 否 |页码|
|pageSize|query|integer| 否 |一页的数量|
|orderItems[0].column|query|string| 否 |none|
|orderItems[0].asc|query|boolean| 否 |none|
|name|query|string| 否 |任务名|
|username|query|string| 否 |用户名|
|completion|query|boolean| 否 |任务完成状态|
|completeTime|query|string| 否 |完成时间|
|day|query|integer| 否 |第几天的任务|
|used|query|boolean| 否 |是否被用于生成过新话题|
|doctorName|query|string| 否 |admin|
|Authorization|header|string| 是 |none|

#### 详细说明

**doctorName**: admin
医生姓名

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 查询单个任务记录 - 后台

POST /Background/TaskRecord/info

查询单个任务记录 主要是更新任务记录时，需要返回单个任务记录的部分信息

> Body 请求参数

```json
{
  "uid": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|[ReqUserInfo](#schemarequserinfo)| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 检测所有用户任务池中数量是否足够 - 后台

POST /Background/TaskRecord/checkTaskNumForAllUsers

检测所有用户任务池中数量是否足够

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

## POST 获取昨天任务完成情况

POST /Background/TaskRecord/getYesterdayTaskData

获取昨天任务完成情况

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 0,
  "msg": "",
  "data": {}
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|OK|none|[Result](#schemaresult)|

# 数据模型

<h2 id="tocS_Pet">Pet</h2>

<a id="schemapet"></a>
<a id="schema_Pet"></a>
<a id="tocSpet"></a>
<a id="tocspet"></a>

```json
{
  "id": 1,
  "category": {
    "id": 1,
    "name": "string"
  },
  "name": "doggie",
  "photoUrls": [
    "string"
  ],
  "tags": [
    {
      "id": 1,
      "name": "string"
    }
  ],
  "status": "available"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||宠物ID编号|
|category|[Category](#schemacategory)|true|none||分组|
|name|string|true|none||名称|
|photoUrls|[string]|true|none||照片URL|
|tags|[[Tag](#schematag)]|true|none||标签|
|status|string|true|none||宠物销售状态|

#### 枚举值

|属性|值|
|---|---|
|status|available|
|status|pending|
|status|sold|

<h2 id="tocS_Category">Category</h2>

<a id="schemacategory"></a>
<a id="schema_Category"></a>
<a id="tocScategory"></a>
<a id="tocscategory"></a>

```json
{
  "id": 1,
  "name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||分组ID编号|
|name|string|false|none||分组名称|

<h2 id="tocS_Tag">Tag</h2>

<a id="schematag"></a>
<a id="schema_Tag"></a>
<a id="tocStag"></a>
<a id="tocstag"></a>

```json
{
  "id": 1,
  "name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||标签ID编号|
|name|string|false|none||标签名称|

<h2 id="tocS_Result">Result</h2>

<a id="schemaresult"></a>
<a id="schema_Result"></a>
<a id="tocSresult"></a>
<a id="tocsresult"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer|false|none||响应码，1 代表成功; 0 代表失败|
|msg|string|false|none||响应信息 描述字符串|
|data|object|false|none||返回的数据|

<h2 id="tocS_ReqUserInfo">ReqUserInfo</h2>

<a id="schemarequserinfo"></a>
<a id="schema_ReqUserInfo"></a>
<a id="tocSrequserinfo"></a>
<a id="tocsrequserinfo"></a>

```json
{
  "uid": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|uid|integer(int64)|true|none||id|

<h2 id="tocS_ReqUpdateUserPassword">ReqUpdateUserPassword</h2>

<a id="schemarequpdateuserpassword"></a>
<a id="schema_ReqUpdateUserPassword"></a>
<a id="tocSrequpdateuserpassword"></a>
<a id="tocsrequpdateuserpassword"></a>

```json
{
  "password": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|password|string|true|none||用户密码|

<h2 id="tocS_ReqLogin">ReqLogin</h2>

<a id="schemareqlogin"></a>
<a id="schema_ReqLogin"></a>
<a id="tocSreqlogin"></a>
<a id="tocsreqlogin"></a>

```json
{
  "username": "string",
  "password": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|username|string|true|none||用户名|
|password|string|true|none||密码|

<h2 id="tocS_UserLoginRecordModel">UserLoginRecordModel</h2>

<a id="schemauserloginrecordmodel"></a>
<a id="schema_UserLoginRecordModel"></a>
<a id="tocSuserloginrecordmodel"></a>
<a id="tocsuserloginrecordmodel"></a>

```json
{
  "id": 0,
  "uid": 0,
  "loginTime": "string",
  "place": "string",
  "device": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||登录记录ID|
|uid|integer(int64)|false|none||用户ID|
|loginTime|string|false|none||登录时间|
|place|string|false|none||登录地点|
|device|string|false|none||登录设备|

<h2 id="tocS_ReqRegisterUser">ReqRegisterUser</h2>

<a id="schemareqregisteruser"></a>
<a id="schema_ReqRegisterUser"></a>
<a id="tocSreqregisteruser"></a>
<a id="tocsreqregisteruser"></a>

```json
{
  "username": "string",
  "name": "string",
  "gender": true,
  "birthday": "string",
  "phone": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|username|string|false|none||用户名|
|name|string|false|none||姓名|
|gender|boolean|false|none||性别|
|birthday|string|false|none||生日|
|phone|string|false|none||手机号|

<h2 id="tocS_ReqUpdateUser">ReqUpdateUser</h2>

<a id="schemarequpdateuser"></a>
<a id="schema_ReqUpdateUser"></a>
<a id="tocSrequpdateuser"></a>
<a id="tocsrequpdateuser"></a>

```json
{
  "id": 0,
  "name": "string",
  "gender": true,
  "birthday": "string",
  "phone": "string",
  "doctor_id": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||用户id|
|name|string|true|none||用户姓名|
|gender|boolean|true|none||性别|
|birthday|string|true|none||生日|
|phone|string|true|none||手机号|
|doctor_id|integer(int64)|false|none||管理员可修改<br />医生id|

<h2 id="tocS_ReqUpdatingInfo">ReqUpdatingInfo</h2>

<a id="schemarequpdatinginfo"></a>
<a id="schema_ReqUpdatingInfo"></a>
<a id="tocSrequpdatinginfo"></a>
<a id="tocsrequpdatinginfo"></a>

```json
{
  "version": "string",
  "platform": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|version|string|true|none||id|
|platform|string|true|none||平台|

<h2 id="tocS_MapString">MapString</h2>

<a id="schemamapstring"></a>
<a id="schema_MapString"></a>
<a id="tocSmapstring"></a>
<a id="tocsmapstring"></a>

```json
{
  "key": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|string|false|none||none|

<h2 id="tocS_ReqRegisterDoctor">ReqRegisterDoctor</h2>

<a id="schemareqregisterdoctor"></a>
<a id="schema_ReqRegisterDoctor"></a>
<a id="tocSreqregisterdoctor"></a>
<a id="tocsreqregisterdoctor"></a>

```json
{
  "username": "string",
  "password": "string",
  "name": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|username|string|true|none||医生用户名|
|password|string|true|none||密码|
|name|string|true|none||医生姓名|

<h2 id="tocS_ReqUpdateDoctor">ReqUpdateDoctor</h2>

<a id="schemarequpdatedoctor"></a>
<a id="schema_ReqUpdateDoctor"></a>
<a id="tocSrequpdatedoctor"></a>
<a id="tocsrequpdatedoctor"></a>

```json
{
  "id": 0,
  "username": "string",
  "name": "string",
  "create_time": "string",
  "deleted": true,
  "login_time": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||医生ID|
|username|string|false|none||医生用户名|
|name|string|false|none||姓名|
|create_time|string|false|none||创建时间|
|deleted|boolean|false|none||逻辑删除|
|login_time|string|false|none||登陆时间|

<h2 id="tocS_ReqUpdateTaskPool">ReqUpdateTaskPool</h2>

<a id="schemarequpdatetaskpool"></a>
<a id="schema_ReqUpdateTaskPool"></a>
<a id="tocSrequpdatetaskpool"></a>
<a id="tocsrequpdatetaskpool"></a>

```json
{
  "id": 0,
  "name": "string",
  "prompt": "string",
  "used": true,
  "remark": "string",
  "deleted": false
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||任务id|
|name|string|true|none||任务名称|
|prompt|string|true|none||大模型提示词prompt|
|used|boolean|true|none||任务是否被完成过|
|remark|string|false|none||备注|
|deleted|boolean|false|none||是否逻辑删除|

<h2 id="tocS_ReqUpdateVersion">ReqUpdateVersion</h2>

<a id="schemarequpdateversion"></a>
<a id="schema_ReqUpdateVersion"></a>
<a id="tocSrequpdateversion"></a>
<a id="tocsrequpdateversion"></a>

```json
{
  "id": 0,
  "platform": "string",
  "version": "string",
  "url": "string"
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||id|
|platform|string|true|none||平台|
|version|string|true|none||版本|
|url|string|true|none||软件更新地址|

<h2 id="tocS_MapLong">MapLong</h2>

<a id="schemamaplong"></a>
<a id="schema_MapLong"></a>
<a id="tocSmaplong"></a>
<a id="tocsmaplong"></a>

```json
{
  "key": 0
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|integer|false|none||none|

<h2 id="tocS_MapObject">MapObject</h2>

<a id="schemamapobject"></a>
<a id="schema_MapObject"></a>
<a id="tocSmapobject"></a>
<a id="tocsmapobject"></a>

```json
{
  "key": {}
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|key|object|false|none||none|

<h2 id="tocS_ReqUpdateTaskRecord">ReqUpdateTaskRecord</h2>

<a id="schemarequpdatetaskrecord"></a>
<a id="schema_ReqUpdateTaskRecord"></a>
<a id="tocSrequpdatetaskrecord"></a>
<a id="tocsrequpdatetaskrecord"></a>

```json
{
  "id": 0,
  "completion": true,
  "score": 0,
  "remark": "string",
  "time": 0,
  "used": true
}

```

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|true|none||任务id|
|completion|boolean|false|none||任务完成状态|
|score|integer|false|none||任务得分|
|remark|string|false|none||备注|
|time|integer|false|none||设定任务最大完成时间|
|used|boolean|false|none||是否被用于生成过新话题|

