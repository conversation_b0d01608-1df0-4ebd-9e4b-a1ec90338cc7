#server.port=8080
#
#mybatis.mapper-locations=classpath:mappers/*xml
#
#mybatis.type-aliases-package=com.example.atis_web_server.mybatis.entity
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

mybatis.configuration.map-underscore-to-camel-case=true

#JavaMailSender ???????
spring.mail.host=smtp.163.com
spring.mail.username=<EMAIL>
spring.mail.password=YKmPbGUKUW2ejixL
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true


app.taskNumPerDay=5

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB