import { VuexModule, Module, Action, Mutation, getModule } from 'vuex-module-decorators'
import { login, userLogout } from '@/api/admin'
import { getToken, setToken, removeToken, getStoreId, setStoreId, removeStoreId, setUserInfo, getUserInfo, removeUserInfo } from '@/utils/cookies'
import store from '@/store'
import Cookies from 'js-cookie'
import { Message } from 'element-ui'

export interface IUserState {
    token: string
    name: string
    avatar: string
    storeId: string
    introduction: string
    userInfo: any
    roles: string[]
    username: string
}

@Module({ 'dynamic': true, store, 'name': 'user' })
class User extends VuexModule implements IUserState {
    public token = getToken() || ''
    public name = ''
    public avatar = ''
    // @ts-ignore
    public storeId: string = getStoreId() || ''
    public introduction = ''
    public userInfo = {}
    public roles: string[] = []
    public username = Cookies.get('username') || ''

    // 设置 token 的 mutation
    @Mutation
    private SET_TOKEN(token: string) {
        this.token = token
    }

    // 设置 name 的 mutation
    @Mutation
    private SET_NAME(name: string) {
        this.name = name
    }

    // 设置 userInfo 的 mutation
    @Mutation
    private SET_USERINFO(userInfo: any) {
        this.userInfo = { ...userInfo }
    }

    // 设置 avatar 的 mutation
    @Mutation
    private SET_AVATAR(avatar: string) {
        this.avatar = avatar
    }

    // 设置 introduction 的 mutation
    @Mutation
    private SET_INTRODUCTION(introduction: string) {
        this.introduction = introduction
    }

    // 设置 roles 的 mutation
    @Mutation
    private SET_ROLES(roles: string[]) {
        this.roles = roles
    }

    // 设置 storeId 的 mutation
    @Mutation
    private SET_STOREID(storeId: string) {
        this.storeId = storeId
    }

    // 设置 username 的 mutation
    @Mutation
    private SET_USERNAME(name: string) {
        this.username = name
    }

    // 登录的 action
    @Action
    public async Login(userInfo: { username: string, password: string }) {
        let { username, password } = userInfo
        username = username.trim()
        const { data } = await login({ username, password })
        if (String(data.code) === '200') {
            // 设置 vuex 中属性的值
            this.SET_USERNAME(username)
            this.SET_TOKEN(data.data.token)
            this.SET_USERINFO(data.data)

            // 保存到 Cookie 中
            Cookies.set('username', username)
            Cookies.set('user_id', data.data.uid)  // 增加了 保存用户id
            Cookies.set('user_info', data.data)
            Cookies.set("token", data.data.token)
            return data
        } else {
            return Message.error(data.msg)
        }
    }

    // 重置 token 的 action
    @Action
    public ResetToken() {
        removeToken()
        this.SET_TOKEN('')
        this.SET_ROLES([])
    }

    // 切换 store 的 action
    @Action
    public async changeStore(data: any) {
        this.SET_STOREID = data.data
        this.SET_TOKEN(data.authorization)
        setStoreId(data.data)
        setToken(data.authorization)
    }

    // 获取用户信息的 action
    @Action
    public async GetUserInfo() {
        if (this.token === '') {
            throw Error('GetUserInfo: token is undefined!')
        }

        const data = JSON.parse(<string>getUserInfo()) //  { roles: ['admin'], name: 'zhangsan', avatar: '/login', introduction: '' }
        if (!data) {
            throw Error('Verification failed, please Login again.')
        }

        const { roles, name, avatar, introduction, applicant, storeManagerName, storeId = '' } = data // data.user
        // roles 必须是一个非空数组
        if (!roles || roles.length <= 0) {
            throw Error('GetUserInfo: roles must be a non-null array!')
        }

        this.SET_ROLES(roles)
        this.SET_USERINFO(data)
        this.SET_NAME(name || applicant || storeManagerName)
        this.SET_AVATAR(avatar)
        this.SET_INTRODUCTION(introduction)
    }

    // 登出的 action
    @Action
    public async LogOut() {
        // const { data } = await userLogout({})  // 后端的退出功能还未实现 所以此处暂时无法调用 退出api 发送请求
        removeToken()
        this.SET_TOKEN('')
        this.SET_ROLES([])
        Cookies.remove('username')
        Cookies.remove('user_info')
        removeUserInfo()
    }
}

export const UserModule = getModule(User)
