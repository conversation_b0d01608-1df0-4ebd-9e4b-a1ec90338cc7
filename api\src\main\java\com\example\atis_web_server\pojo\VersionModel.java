package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = ConstModelNameAttribute.VERSION)
@TableName(ConstModelNameAttribute.VERSION)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class VersionModel {
    @Id  //标注主键
    @TableId(type = IdType.AUTO) //mybatis-plus注解
    @GeneratedValue(strategy = GenerationType.IDENTITY) //id自增策略
    @ApiModelProperty("ID")
    private Long id;
    @ApiModelProperty("平台")
    private String platform;
    @ApiModelProperty("版本")
    private String version;
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    @ApiModelProperty("更新网址")
    private String url;
}
