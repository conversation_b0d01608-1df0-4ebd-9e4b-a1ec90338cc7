package com.example.atis_web_server.controller;

import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.dto.resp.RespTaskInfo;
import com.example.atis_web_server.service.impl.TaskRecordService;
import com.example.atis_web_server.service.impl.UserService;
import com.example.atis_web_server.utils.Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@Api(tags = "客户端任务记录管理接口")
@RequestMapping(value = "/client")
public class TaskRecordController {
    @Autowired
    private TaskRecordService taskRecordService;
    @Autowired
    private UserService userService;

    private static final Logger logger = LoggerFactory.getLogger(TaskRecordController.class);

    /**
     * 下发任务
     *
     * @param ??json格式?? user_id
     * @return
     */
    @ApiOperation("用户获取当天任务")
    @PostMapping("/GetTask")
    public Result getTask(@RequestHeader(Utils.TOKEN) String token) throws Exception {
        Long uid = Utils.getUidByToken(token);
        RespTaskInfo respTaskInfo = taskRecordService.getByDay(uid);
        if (respTaskInfo == null) {
            return Result.success(404, null);  // 今日任务已全部完成
        } else {
            return Result.success(respTaskInfo);
        }
    }

    /**
     * 任务完成
     */
    @ApiOperation("标记任务完成")
    @PostMapping("/TaskIsComplete")
    public Result taskIsComplete(@RequestBody Map<String, Long> obj) {
        logger.info("*** 任务完成，id:{}", obj.get("id"));
        // 设置任务已完成,并设置任务完成的时间
        taskRecordService.updateCompletedById(obj.get("id"));
        return Result.success("上传任务完成操作成功");
    }

    /**
     * 上传聊天记录
     *
     * @param chatHistory
     * @return
     */
    @ApiOperation("添加聊天记录")
    @PostMapping("/UploadChatHistory")
    public Result uploadChatHistory(@RequestBody Map<String, Object> chatHistory) throws Exception {

        Integer uid = (Integer) chatHistory.get("uid");
        Integer id = (Integer) chatHistory.get("id");
        String fileData = (String) chatHistory.get("fileData");
        Boolean exception = (Boolean) chatHistory.get("exception");

        logger.info("*** 上传聊天记录，uid:{},id:{}", uid, id);
        taskRecordService.updateChatHistoryById(id, fileData, uid, exception);
        return Result.success("上传聊天记录成功");
    }

    /**
     * 上传EEG
     */
    @ApiOperation("上传EEG")
    @PostMapping("/UploadEEG")
    public Result uploadLog(
            @RequestHeader(Utils.TOKEN) String token,
            @RequestPart("file") @NotNull MultipartFile file
    ) throws Exception {
        try {
            // 获取文件名
            String fileName = file.getOriginalFilename();
            if (fileName == null || fileName.isEmpty()) {
                return Result.error(400, "文件名为空");
            }

            // 验证文件名安全性
            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                return Result.error(400, "非法的文件名");
            }

            // 获取文件数据
            byte[] fileData = file.getBytes();
            if (fileData.length == 0) {
                return Result.error(400, "文件数据为空");
            }

            // 获取用户信息
            Long uid = Utils.getUidByToken(token);
            String name = userService.getById(uid).getName();

            // 构造保存路径
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String dateDir = dateFormatter.format(LocalDateTime.now());
            Path path = Paths.get("appLogs", "EEG", String.format("%s-%s", uid.toString(), name), dateDir, fileName);

            // 创建目录
            Files.createDirectories(path.getParent());

            // 保存文件
            Files.write(path, fileData, StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING);

            return Result.success();
        } catch (IOException e) {
            logger.error("EEG 文件上传失败: {}", e.getMessage());
            return Result.error(500, "服务器内部错误");
        } catch (Exception e) {
            logger.error("EEG 文件上传异常: {}", e.getMessage());
            return Result.error(400, "请求数据格式错误");
        }
    }

    /**
     * 返回任务进度
     */
    @ApiOperation("返回任务进度")
    @PostMapping("/GetTaskScheduleUrl")
    public Result getTaskScheduleUrl(@RequestHeader(Utils.TOKEN) String token) {
        Long uid = Utils.getUidByToken(token);
        Map<String, Object> obj = new HashMap<>();
        var schedule = taskRecordService.getScheduleByUid(uid);
        obj.put("schedule", schedule);
        logger.info("*** schedule:{} ", schedule);
        return Result.success(obj);
    }
}
