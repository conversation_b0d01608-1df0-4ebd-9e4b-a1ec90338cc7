package com.example.atis_web_server.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "任务池更新实体")
public class ReqUpdateTaskPool {
    @NotNull
    @ApiModelProperty("任务id")
    private Long id;
    @NotNull
    @ApiModelProperty("任务名称")
    private String name;
    @NotNull
    @ApiModelProperty("大模型提示词prompt")
    private String prompt;
    @NotNull
    @ApiModelProperty("任务是否被完成过")
    private Boolean used;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否逻辑删除")
    private Boolean deleted = false;
}
