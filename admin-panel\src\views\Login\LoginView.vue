<template>
    <div class="login">
        <div class="login-box">
            <div class="login-form">
                <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
                    <div class="login-form-title">
                        <span class="title-label">ATIS 后台管理系统</span>
                    </div>
                    <el-form-item prop="username">
                        <el-input
                            v-model="loginForm.username"
                            type="text"
                            auto-complete="off"
                            placeholder="账号"
                        />
                    </el-form-item>
                    <el-form-item prop="password">
                        <el-input
                            v-model="loginForm.password"
                            type="password"
                            placeholder="密码"
                            @keyup.enter.native="handleLogin"
                        />
                    </el-form-item>
                    <el-form-item style="width: 100%">
                        <el-button
                            :loading="loading"
                            class="login-btn"
                            size="medium"
                            type="primary"
                            style="width: 100%"
                            @click.native.prevent="handleLogin"
                        >
                            <span v-if="!loading">登录</span>
                            <span v-else>登录中...</span>
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator'
import { Route } from 'vue-router'
import { Form as ElForm, Input } from 'element-ui'
import { UserModule } from '@/store/modules/user'

@Component({
    name: 'Login',
})
export default class extends Vue {
    $refs!: {
        loginForm: ElForm
    };

    private validateUsername = (rule: any, value: string, callback: Function) => {
        if (!value) {
            callback(new Error('请输入用户名'))
        } else {
            callback()
        }
    }
    private validatePassword = (rule: any, value: string, callback: Function) => {
        if (value.length < 6) {
            callback(new Error('密码必须在6位以上'))
        } else {
            callback()
        }
    }
    private loginForm = {
        username: '',
        password: '',
    } as {
        username: String
        password: String
    }

    loginRules = {
        username: [{ validator: this.validateUsername, trigger: 'blur' }],
        password: [{ validator: this.validatePassword, trigger: 'blur' }],
    }
    private loading = false
    private redirect?: string

    @Watch('$route', { immediate: true })
    private onRouteChange(route: Route) {}

    // 登录
    private handleLogin() {
        (this.$refs.loginForm as ElForm).validate(async (valid: boolean) => {
            if (valid) {
                this.loading = true
                await UserModule.Login(this.loginForm as any)
                    .then((res: any) => {
                        if (String(res.code) === '200') {
                            //登录成功，跳转到系统首页
                            console.log('登录成功')
                            this.$router.push('doctor')
                        } else {
                            this.$message.error(res.msg)
                            this.loading = false
                        }
                    })
                    .catch(() => {
                        this.$message.error('用户名或密码错误！')
                        this.loading = false
                    })
            } else {
                return false
            }
        })
    }
}
</script>

<style scoped>
.login {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f5f5f5;
}

.login-box {
    width: 400px;
    padding: 40px;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.login-form-title {
    text-align: center;
    margin-bottom: 20px;
}

.title-label {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.el-form-item {
    margin-bottom: 20px;
}

.login-btn {
    width: 100%;
    height: 40px;
    font-size: 16px;
}
</style>