import request from '@/utils/request'
/**
 *
 * 管理员和医生 管理
 *
 **/
// 登录
export const login = (data: any) =>
  request({
    'url': '/doctors/login',
    'method': 'post',
    data: data
  })

  // 退出
 export const userLogout = (params: any) =>
 request({
   'url': `/doctors/logout`,
   'method': 'post',
   params
 })

   // 分页查询 用户表
   export const getUsersList = (params: any) =>
   request({
     'url': `/users/queryPage`,
     'method': 'get',
     'params': params //查询搜索参数,分页参数（排序参数）
   })

// 启用禁用用户账号
export const enableOrDisableUser = (params: any) =>
request({
  'url': `/users/status/${params.status}`,
  'method': 'POST',
  'params': {id: params.id}
})

// 新增用户
export const addUser = (params: any) =>
request({
  'url': '/users/register',
  'method': 'POST',
  'data': params
})

// 根据id查询用户
export const queryUserById = (id: number) =>
request({
    'url': `/users/info`,
    'method': 'POST',
    'data': {uid: id}
})

// 修改用户
export const updateUser = (params: any) =>
request({
  'url': '/users/update',
  'method': 'POST',
  'data': params
})

// 新增医生
export const addDoctor = (params: any) =>
request({
  'url': '/doctors/register',
  'method': 'POST',
  'data': params
})

// 分页查询 医生表
export const getDoctorsList = (params: any) =>
request({
  'url': `/doctors/queryPage`,
  'method': 'get',
  'params': params //查询搜索参数,分页参数（排序参数）
})

// 根据id查询医生
export const queryDoctorById = (id: number) =>
request({
    'url': `/doctors/info/${id}`,
    'method': 'POST',
    'data': {id: id}
})

// 修改医生
export const updateDoctor = (params: any) =>
request({
  'url': '/doctors/update',
  'method': 'POST',
  'data': params
})

// 启用禁用医生账号
export const enableOrDisableDoctor = (params: any) =>
request({
  'url': `/doctors/status/${params.status}`,
  'method': 'POST',
  'params': {id: params.id}
})

