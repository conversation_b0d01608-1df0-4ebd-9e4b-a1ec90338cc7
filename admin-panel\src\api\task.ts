import request from '@/utils/request'
/**
 *
 * 任务池管理
 *
 **/

// 查询 任务池 列表
export const getTaskPoolList = (params: any) =>
  request({
    'url': '/TaskPool/queryPage',
    'method': 'get',
    'params': params
  })


// 新增 任务池任务
export const addTaskPool = (params: any) =>
    request({
        'url': '/TaskPool/add',
        'method': 'POST',
        'data': params
    })

// 修改 任务池任务
export const updateTaskPool = (params: any) =>
    request({
        'url': '/TaskPool/update',
        'method': 'POST',
        'data': params
    })

// 删除 任务池任务
// export const deleteTaskPool = (params: any) =>
//     request({
//         'url': '/TaskPool/delete',
//         'method': 'DELETE',
//         'data': params
//     })

// 启用/禁用 任务池任务
export const enableOrDisableTaskPool = (params: any) =>
    request({
        'url': `/TaskPool/status/${params.status}`,
        'method': 'POST',
        'params': { id: params.id }
    })
// 根据id查询任务池任务
export const queryTaskPoolById = (id: number) =>
    request({
        'url': `/TaskPool/info`,
        'method': 'POST',
        'data': {uid: id}
    })
/**
 *
 * 任务记录管理
 *
 **/

// 查询 任务记录 列表
export const getTaskRecordList = (params: any) =>
    request({
      'url': '/TaskRecord/queryPage',
      'method': 'get',
      'params': params
    })

// 新增 任务记录
export const addTaskRecord = (params: any) =>
    request({
        'url': '/taskRecord/add',
        'method': 'POST',
        'data': params
    })

// 修改 任务记录
export const updateTaskRecord = (params: any) =>
    request({
        'url': '/TaskRecord/update',
        'method': 'POST',
        'data': params
    })

// 根据id查询 任务记录
export const queryTaskRecordById = (id: number) =>
    request({
        'url': `/TaskRecord/info`,
        'method': 'POST',
        'data': {uid: id}
    })

  