package com.example.atis_web_server.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "用户更新实体")
public class ReqUpdateUser {
    @NotNull
    @ApiModelProperty("用户id")
    private Long id;
    @NotNull
    @ApiModelProperty("用户姓名")
    private String name;
    @NotNull
    @ApiModelProperty("性别")
    private Boolean gender;
    @NotNull
    @ApiModelProperty("生日")
    private LocalDate birthday;
    @NotNull
    @ApiModelProperty("手机号")
    private String phone;
    @ApiModelProperty("医生id")
    private Long doctor_id;  // 管理员可修改
}
