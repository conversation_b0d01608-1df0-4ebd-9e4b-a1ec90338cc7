package com.example.atis_web_server.service.impl;

import com.example.atis_web_server.mapper.TaskRecordMapper;
import com.example.atis_web_server.pojo.TaskRecordModel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MailService {

    @Autowired
    private JavaMailSender mailSender;
    @Autowired
    private TaskRecordMapper taskRecordMapper;

    @Value("${spring.mail.username}")
    private String form;

    private static final Logger logger = LoggerFactory.getLogger(MailService.class);

    @PostConstruct
    public void init() {
        // 在这里执行你的任务
        logger.info("邮箱服务已初始化，执行启动时任务...");
    }

    public void sendHtmlMail(String to, String subject, String content) {
        MimeMessage message = mailSender.createMimeMessage();
        try {
            // true表示需要创建一个multipart message
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            // 设置发件人名称和邮箱地址
            helper.setFrom(MimeUtility.encodeWord("ASD任务反馈") + " <" + form + ">");
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(content, true);
            mailSender.send(message);
            logger.info("邮件发送成功");
        } catch (Exception e) {
            logger.error("邮件发送失败", e);
        }
    }
}