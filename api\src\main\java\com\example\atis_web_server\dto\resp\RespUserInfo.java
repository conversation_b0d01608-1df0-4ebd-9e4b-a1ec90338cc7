package com.example.atis_web_server.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDate;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "用户信息查询请求实体-后台")
public class RespUserInfo {
    @ApiModelProperty("用户id")
    private Long id;
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("性别")
    private Boolean gender;
    @ApiModelProperty("生日")
    private LocalDate birthday;
    @ApiModelProperty("年龄")
    private Integer age;
    @ApiModelProperty("手机号")
    private String phone;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("登录时间")
    private LocalDateTime loginTime;
    @ApiModelProperty("注册天数")
    private Integer day;
    @ApiModelProperty("医生ID")
    private Long doctorId;
    @ApiModelProperty("医生姓名")
    private String doctorName;
    @ApiModelProperty("是否被逻辑删除")
    private Boolean deleted;
}
