package com.example.atis_web_server.dto.req;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "更新用户密码实体")
public class ReqUpdateUserPassword {
    @NotNull
    @ApiModelProperty("用户密码")
    private String password;
}
