package com.example.atis_web_server.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "更新医生信息")
public class ReqUpdateDoctor {
    @ApiModelProperty("医生ID")
    private Long id;
    @ApiModelProperty("医生用户名")
    private String username;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("创建时间")
    private LocalDateTime create_time;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted;
    @ApiModelProperty("登陆时间")
    private LocalDateTime login_time;
}
