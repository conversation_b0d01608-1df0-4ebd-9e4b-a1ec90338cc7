<template>
    <div>
        <!-- 搜索栏 -->
        <div class="tableBar">
            <label style="margin-right: 5px">姓名:</label>
            <el-input v-model="name" placeholder="请输入姓名" style="width: 8%" />

            <label style="margin-right: 5px; margin-left: 20px">用户名:</label>
            <el-input v-model="username" placeholder="请输入账号名" style="width: 8%" />

            <label style="margin-right: 5px; margin-left: 20px">年龄:</label>
            <el-input v-model="age" placeholder="请输入用户年龄" style="width: 8%" type="number" />

            <label style="margin-right: 5px; margin-left: 20px">医生:</label>
            <el-input v-model="doctorName" placeholder="请输入医生姓名" style="width: 8%" />

            <label style="margin-right: 5px; margin-left: 20px">注册天数:</label>
            <el-input v-model="day" placeholder="天数" style="width: 4%" type="number" />

            <label style="margin-right: 5px; margin-left: 20px"> 状态:</label>
            <el-select v-model="deleted" placeholder="是否已删除" style="width: 8%">
                <el-option label="否" value="1"></el-option>
                <el-option label="是" value="2"></el-option>
            </el-select>

            <!-- 搜索按钮 新增按钮 -->
            <el-button type="primary" style="margin-left: 20px" @click="pageQuery">查询</el-button>
            <el-button type="primary" style="float: right" @click="handleAddUser">+新增用户</el-button>
        </div>
        <br><br>

        <!-- 表单 -->
        <!-- <el-form :inline="true" :model="searchForm" class="demo-forminline">
            <el-form-item label="姓名">
                <el-input v-model="searchForm.name" placeholder="请输入姓名" style="width: 20;"></el-input>
            </el-form-item>
            <el-form-item label="性别">
                <el-select v-model="searchForm.gender" placeholder="性别">
                    <el-option label="男" value="1"></el-option>
                    <el-option label="女" value="2"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="入职日期">
                <el-date-picker v-model="searchForm.entrydate" type="daterange" range-separator="至"
                    start-placeholder="开始日期" end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="pageQuery">查询</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="addUser" style="float: right;">+新增用户</el-button>
            </el-form-item>

        </el-form> -->

        <!-- 表格 -->
        <!-- <el-table :data="tableData">
            <el-table-column prop="name" label="姓名" width="180"></el-table-column>
            <el-table-column prop="image" label="图像" width="180">
                <template slot-scope="scope">
                    <img :src="scope.row.image" width="100px" height="70px">
                </template>
            </el-table-column>
            <el-table-column prop="gender" label="性别" width="140">
                <template slot-scope="scope">
                    {{ scope.row.gender == 1 ? "男" : "女" }}
                </template>
            </el-table-column>
            <el-table-column prop="job" label="职位" width="140"></el-table-column>
            <el-table-column prop="entrydate" label="入职日期" width="180"></el-table-column>
            <el-table-column prop="updatetime" label="最后操作时间" width="230"></el-table-column>
            <el-table-column label="操作">
                <el-button type="primary" size="mini">编辑</el-button>
                <el-button type="danger" size="mini">删除</el-button>
            </el-table-column>
        </el-table> -->


        <!-- 测试表格数据 -->
        <!-- <el-table
        :data="records"
        stripe
        style="width: 100%">
        <el-table-column
          prop="name"
          label="员工姓名"
          width="180">
        </el-table-column>
        <el-table-column
          prop="username"
          label="账号"
          width="180">
        </el-table-column>
        <el-table-column
          prop="phone"
          label="手机号">
        </el-table-column>
        <el-table-column
          prop="status"
          label="账号状态">
          <template slot-scope="scope">
            {{scope.row.status === 0 ? '禁用' : '启用'}}
          </template>
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="最后操作数据">
        </el-table-column> -->

        <!-- 查询到的表格数据 -->
        <el-table :data="records" stripe style="width: 100%">
            <!-- 序号 -->
            <el-table-column label="序号" width="50">
                <template slot-scope="scope">
                    {{ (pageNo - 1) * pageSize + scope.$index + 1 }}
                </template>
            </el-table-column>
            
            <el-table-column prop="name" label="姓名" width="180">
            </el-table-column>
            <el-table-column prop="username" label="账号名" width="180">
            </el-table-column>
            <el-table-column prop="phone" label="手机号">
            </el-table-column>
            <el-table-column prop="gender" label="性别">
                <template slot-scope="scope">
                    {{ scope.row.gender === true ? '男' : '女' }}
                </template>
            </el-table-column>
            <el-table-column prop="age" label="年龄">
            </el-table-column>
            <el-table-column prop="birthday" label="生日">
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                    {{ new Date(scope.row.createTime).toLocaleString() }}
                </template>
            </el-table-column>
            <el-table-column prop="day" label="注册天数">
            </el-table-column>
            <el-table-column prop="deleted" label="状态">
                <template slot-scope="scope">
                    {{ scope.row.deleted === true ? '已禁用' : '启用中' }}
                </template>
            </el-table-column>
            <el-table-column prop="doctorName" label="医生姓名">
            </el-table-column>
            <el-table-column prop="loginTime" label="登录时间">
                <template slot-scope="scope">
                    {{ new Date(scope.row.loginTime).toLocaleString() }}
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="text" @click="handleUpdateUser(scope.row)">修改</el-button>
                    <el-button type="text" @click="handleStartOrStop(scope.row)">{{scope.row.deleted === false ? '禁用' :
                        '启用'}}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <br><br>

        <!-- 原始Pagination分页 -->
        <!-- <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" background
            layout="sizes,prev, pager, next,jumper,total" :total="1000">
        </el-pagination> -->
        
        <!-- Pagination分页 -->
        <el-pagination class="pageList" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="pageNo" :page-sizes="[10, 20, 30, 40, 50]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total" style="text-align: center;">
        </el-pagination>
    </div>
</template>

<script>
import axios from 'axios';
import {getUsersList, enableOrDisableUser} from '@/api/admin'

export default {
    data() {
        return {
            name: '', //用户姓名，对应上面的输入框
            username: '', //用户账号，对应上面的输入框
            age: '', //用户年龄，对应上面的输入框
            doctorName: '', //医生姓名，对应上面的输入框
            day: '', //注册天数，对应上面的输入框
            deleted: '', //是否已删除，对应上面的下拉框

            pageNo: 1, //当前页码
            pageSize: 10, //每页记录数
            
            total: 0, //总记录数
            records: [] //当前页要展示的数据集合
        }
    },
    methods: {
        create () {
            this.pageQuery();
        },
         // 查询用户
        pageQuery() {
            // 请求参数
            const params = {};
            if (this.name) params.name = this.name;
            if (this.username) params.username = this.username;
            if (this.age) params.age = this.age;
            if (this.doctorName) params.doctorName = this.doctorName;
            if (this.day) params.day = this.day;
            if (this.deleted) params.deleted = this.deleted;
            params.pageNo = this.pageNo;
            params.pageSize = this.pageSize;
            // 请求后台数据，获取用户列表（调用api接口）
            getUsersList(params).then(resp => {
                if (resp.data.code == 200) {
                    this.records = resp.data.data.list;
                    this.total = resp.data.data.total;
                }
            }).catch(error => {
                this.$message.error("查询用户失败"+error.message);
            });
            
        },
        //pageSize发生变化时触发
        handleSizeChange(pageSize) {
            this.pageSize = pageSize
            this.pageQuery()
        },
        //pageNo发生变化时触发
        handleCurrentChange(pageNo) {
            this.pageNo = pageNo
            this.pageQuery()
        },
        // 启用禁用 用户
        handleStartOrStop(row) {
            // 判断是否为管理员账号
            if (row.username === 'admin') {
                this.$message.error('admin为系统的管理员账号，不能更改账号状态！')
                return
            }
            //弹出确认提示框
            this.$confirm('确认要修改当前用户账号的状态吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const p = {
                    id: row.id,
                    status: !row.deleted ? true : false
                }
                enableOrDisableUser(p).then(res => {
                    if (res.data.code === 200) {
                        this.$message.success('用户的账号状态修改成功！')
                        this.pageQuery()  //重新查询数据
                    }
                })
            }).catch(() => {
                this.$message.info('已取消修改')
            })
        },
        //跳转到 新增用户页面（组件）
        handleAddUser() {
            // 传递当前页码和查询条件
            this.$router.push({ path: '/user/add', query: { pageNo: this.pageNo, ...this.getQueryParams() } });
        },
        //跳转到 修改用户页面（组件）
        handleUpdateUser(row) {
            if (row.username === 'admin') {
                this.$message.error('admin为系统的管理员账号，不能修改！')
                return
            }
            // 传递当前页码和查询条件
            this.$router.push({ path: '/user/add', query: { id: row.id, pageNo: this.pageNo, ...this.getQueryParams() } });
        },
        // 获取查询参数
        getQueryParams() {
            return {
                name: this.name,
                username: this.username,
                age: this.age,
                doctorName: this.doctorName,
                day: this.day,
                deleted: this.deleted
            };
        },
        // 在 mounted 中恢复查询条件和页码
        // mounted() {
        //     const query = this.$route.query;
        //     if (query.pageNo) {
        //         this.pageNo = Number(query.pageNo);
        //     }
        //     this.name = query.name || '';
        //     this.username = query.username || '';
        //     this.age = query.age || '';
        //     this.doctorName = query.doctorName || '';
        //     this.day = query.day || '';
        //     this.deleted = query.deleted || '';
        //     this.pageQuery();
        // },
    },
    mounted() {
        const query = this.$route.query;
        if (query.pageNo) {
            this.pageNo = Number(query.pageNo);
        }
        this.name = query.name || '';
        this.username = query.username || '';
        this.age = query.age || '';
        this.doctorName = query.doctorName || '';
        this.day = query.day || '';
        this.deleted = query.deleted || '';
        this.pageQuery();
    }
    // mounted() {
    //     // axios.get("https://mock.apifox.cn/m1/3128855-0-default/emp/list")
    //     axios.get("http://api.doc.jiyou-tech.com/mock/16727/list")
    //         .then(resp => {
    //             this.tableData = resp.data.data; //响应数据赋值给数据模型
    //         });
    // }

}
</script>
<style></style>