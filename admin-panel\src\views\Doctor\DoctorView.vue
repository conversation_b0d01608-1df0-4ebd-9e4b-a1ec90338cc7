<template>
    <div>

        <el-row type="flex" justify="center" align="middle" style="margin-bottom: 20px;">
            <el-col :span="24" style="text-align: center;">
                <h2>欢迎, ID={{ currentDoctorId }}:{{ currentDoctor }}</h2>
            </el-col>
        </el-row>

         <!-- 搜索栏  只有当登录账号为 超级管理员时才显示 -->
         <div class="tableBar" v-if="this.currentDoctor === 'qjh'">

            <label style="margin-right: 5px">ID:</label>
            <el-input v-model="id" placeholder="请输入ID" style="width: 8%" />

            <label style="margin-right: 5px">姓名:</label>
            <el-input v-model="name" placeholder="请输入医生姓名" style="width: 8%" />

            <label style="margin-right: 5px; margin-left: 20px">用户名:</label>
            <el-input v-model="username" placeholder="请输入账号名" style="width: 8%" />

            <label style="margin-right: 5px; margin-left: 20px"> 状态:</label>
            <el-select v-model="deleted" placeholder="是否已删除" style="width: 8%">
                <el-option label="否" value="1"></el-option>
                <el-option label="是" value="2"></el-option>
            </el-select>

            <!-- 搜索按钮 新增按钮 -->
            <el-button type="primary" style="margin-left: 20px" @click="pageQueryDoctor">查询</el-button>
            <el-button type="primary" style="float: right" @click="handleAddDoctor">+新增医生</el-button>
        </div>
        <br>

        <div v-if="this.currentDoctor != 'admin'">
            <el-row type="flex" justify="center" align="middle">
                <el-col :span="24" style="text-align: center;">
                    <el-button type="primary" @click="queryInfo">查询信息</el-button>
                </el-col>
            </el-row>

        </div>

        <br><br>


        <!-- 测试用的表格 -->
        <!-- <el-table :data="tableData" border>
            <el-table-column prop="name" label="名称" width="250"></el-table-column>
            <el-table-column prop="updatetime" label="最后操作时间" width="250"></el-table-column>
            <el-table-column label="操作">
                <el-button type="primary" size="mini">编辑</el-button>
                <el-button type="danger" size="mini">删除</el-button>
            </el-table-column>
        </el-table> -->


         <!-- 分页查询 表格 -->
         <el-table :data="records" stripe style="width: 100%">

            <!-- 序号 -->
            <!-- <el-table-column label="序号" width="50">
                <template slot-scope="scope">
                    {{ (pageNo - 1) * pageSize + scope.$index + 1 }}
                </template>
            </el-table-column> -->

            <el-table-column prop="id" label="ID" width="180">
            </el-table-column>
            <el-table-column prop="name" label="医生姓名" width="180">
            </el-table-column>
            <el-table-column prop="username" label="用户名" width="180">
            </el-table-column>
            <el-table-column prop="deleted" label="状态">
                <template slot-scope="scope">
                    {{ scope.row.deleted === true ? '已禁用' : '启用中' }}
                </template>
            </el-table-column>

            <el-table-column prop="createTime" label="创建时间">
                <template slot-scope="scope">
                    {{ new Date(scope.row.createTime).toLocaleString() }}
                </template>
            </el-table-column>

            <el-table-column prop="loginTime" label="登录时间">
                <template slot-scope="scope">
                    {{ new Date(scope.row.loginTime).toLocaleString() }}
                </template>
            </el-table-column>
            <el-table-column label="操作">
                <template slot-scope="scope">
                    <el-button type="text" @click="handleUpdateDoctor(scope.row)">修改</el-button>
                    <el-button type="text" @click="handleStartOrStopDoctor(scope.row)">{{scope.row.deleted === false ? '禁用' :
                        '启用'}}</el-button>
                </template>
            </el-table-column>
        </el-table>
        <br><br>



    </div>
</template>

<script>
import axios from 'axios';
// import {getDoctorsList, enableOrDisableDoctor} from '@/api/admin'
import {queryDoctorById} from '@/api/admin'
import Cookies from 'js-cookie'

export default {
    data() {
        return {
            // 当前登录用户,从cookie中获取，cookie是在登录成功后设置的
            currentDoctor:Cookies.get('username'),
            currentDoctorId:Cookies.get('user_id'),

            // tableData: [{
            //     id:1,
            //     name:"赵医生",
            //     updatetime:"2010-01-01 12:00:00"
            // },{
            //     id:2,
            //     name:"王医生",
            //     updatetime:"2010-01-01 12:00:00"
            // },{
            //     id:3,
            //     name:"李医生",
            //     updatetime:"2010-01-01 12:00:00"
            // }],
            records: [{
                id: 1,
                name: "赵医生",
                username: "zhaoyisheng",
                deleted: false,
                createTime: 1612137600000,
                loginTime: 1612137600000
            }, {
                id: 2,
                name: "王医生",
                username: "wangyisheng",
                deleted: false,
                createTime: 1612137600000,
                loginTime: 1612137600000
            }, {
                id: 3,
                name: "李医生",
                username: "liyisheng",
                deleted: false,
                createTime: 1612137600000,
                loginTime: 1612137600000
            }],
            id: '',
            name: '',
            username: '',
            deleted: ''
        }
    },
    methods: {
        // 查询医生
        pageQueryDoctor() {
            alert('查询医生')
            // getDoctorsList({
            //     id: this.id,
            //     name: this.name,
            //     username: this.username,
            //     deleted: this.deleted
            // }).then(res => {
            //     if (res.code === 200) {
            //         this.records = res.data;
            //     } else {
            //         this.$message({
            //             message: res.message,
            //             type: 'error'
            //         });
            //     }
            // });
        },
        // 查询信息
        queryInfo() {
            queryDoctorById(this.currentDoctorId).then(res => {
                if (res.data.code === 200) {
                    const info = {}
                    info.id = res.data.data.uid
                    info.name = res.data.data.name
                    info.username = res.data.data.username
                    info.deleted = false
                    info.createTime = 1612137600000
                    info.loginTime = 1612137600000
                    this.records = [info]
                }
            })
        },
        //启用禁用医生
        handleStartOrStopDoctor(row) {
            alert('启用禁用医生')
            // 判断是否为管理员账号
            // if (row.username === 'admin') {
            //     this.$message.error('admin为系统的管理员账号，不能更改账号状态！')
            //     return
            // }

            // //弹出确认提示框
            // this.$confirm('确认要修改当前用户账号的状态吗?', '提示', {
            //     confirmButtonText: '确定',
            //     cancelButtonText: '取消',
            //     type: 'warning'
            // }).then(() => {
            //     enableOrDisableDoctor(row.id).then(res => {
            //         if (res.code === 200) {
            //             this.$message.success('医生的账号状态修改成功！')
            //             this.pageQueryDoctor(); //重新查询医生列表
            //         } else {
            //             this.$message.error(res.message)
            //         }
            //     })
            // }).catch(() => {
            //     this.$message.info('已取消修改')
            // })
        },
        //新增医生
        handleAddDoctor() {
            // alert('新增医生')
            this.$router.push('/doctor/add')
        },
        //修改医生
        handleUpdateDoctor(row) {
            alert('修改医生')
            // if (row.username === 'admin') {
            //     this.$message.error('admin为系统的管理员账号，不能修改！')
            //     return
            // }
            // this.$router.push({ path: '/doctor/add', query: { id: row.id } })
        },
        mounted() {
            
        }
}
}
</script>

<style>
</style>