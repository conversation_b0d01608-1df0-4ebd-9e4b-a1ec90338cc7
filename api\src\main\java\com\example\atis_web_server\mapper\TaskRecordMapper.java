package com.example.atis_web_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.atis_web_server.pojo.TaskRecordModel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;


public interface TaskRecordMapper extends BaseMapper<TaskRecordModel> {
    @Select("SELECT u.id, u.name, COUNT(t.uid) AS count " +
            "FROM atis.user u " +
            "LEFT JOIN atis.task t ON u.id = t.uid " +
            "AND t.doctor_id = #{doctor_id} " +
            "AND t.completion = true " +
            "AND DATE(t.created_at) = DATE(DATE_SUB(NOW(), INTERVAL 1 DAY)) " +
            "WHERE u.doctor_id = #{doctor_id} AND u.deleted = false " +
            "GROUP BY u.id, u.name")
    List<Map<String, Object>> getYesterdayTaskData(@Param("doctor_id") Long doctor_id);
}