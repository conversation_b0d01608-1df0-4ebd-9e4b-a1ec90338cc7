package com.example.atis_web_server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.atis_web_server.dto.query.PageDTO;
import com.example.atis_web_server.dto.req.ReqUpdateAssets;
import com.example.atis_web_server.mapper.AssetsUpdatingMapper;
import com.example.atis_web_server.pojo.AssetsUpdatingModel;
import com.example.atis_web_server.service.IAssetsUpdatingService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
public class AssetsUpdatingService extends ServiceImpl<AssetsUpdatingMapper, AssetsUpdatingModel> implements IAssetsUpdatingService {
    @Autowired
    private AssetsUpdatingMapper assetsUpdatingMapper;

    private static final Logger logger = LoggerFactory.getLogger(AssetsUpdatingService.class);

    @Override
    public AssetsUpdatingModel getInfo() {
        return assetsUpdatingMapper.selectList(null).get(0);
    }

    @Override
    public List<AssetsUpdatingModel> getAllInfo() {
        return assetsUpdatingMapper.selectList(null);
    }

    @Override
    public boolean updateInfo(AssetsUpdatingModel assetsUpdatingModel) {
        return assetsUpdatingMapper.updateById(assetsUpdatingModel) > 0;
    }

    @Override
    public AssetsUpdatingModel toAssetsUpdatingModel(ReqUpdateAssets req) {
        AssetsUpdatingModel assetsUpdatingModel = assetsUpdatingMapper.selectById(req.getId());
        if (ObjUtil.isEmpty(assetsUpdatingModel)) {
            logger.info("assetsUpdatingModel is null");
            return null;
        }
        assetsUpdatingModel.setId(req.getId());
        assetsUpdatingModel.setUrl(req.getUrl());
        assetsUpdatingModel.setUpdateTime(LocalDateTime.now());
        assetsUpdatingMapper.updateById(assetsUpdatingModel);
        return assetsUpdatingModel;
    }

    @Override
    public PageDTO<AssetsUpdatingModel> queryPage() {
        // 1.构建分页条件
        Page<AssetsUpdatingModel> page = new Page<AssetsUpdatingModel>();

        // 2.分页查询
        Page<AssetsUpdatingModel> p = lambdaQuery().page(page);

        // 3. 封装VO结果
        return PageDTO.of(p, assets -> {
            // 1.拷贝基础属性
            return BeanUtil.copyProperties(assets, AssetsUpdatingModel.class);
        });
    }
}
