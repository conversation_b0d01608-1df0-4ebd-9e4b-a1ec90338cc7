package com.example.atis_web_server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.example.atis_web_server.common.ChatGpt;
import com.example.atis_web_server.dto.query.PageDTO;
import com.example.atis_web_server.dto.req.ReqTaskPoolInfo;
import com.example.atis_web_server.dto.req.ReqUpdateTaskPool;
import com.example.atis_web_server.dto.resp.RespTaskPoolInfo;
import com.example.atis_web_server.mapper.TaskDefaultMapper;
import com.example.atis_web_server.mapper.TaskPoolMapper;
import com.example.atis_web_server.mapper.TaskRecordMapper;
import com.example.atis_web_server.mapper.UserMapper;
import com.example.atis_web_server.pojo.TaskDefaultModel;
import com.example.atis_web_server.pojo.TaskPoolModel;
import com.example.atis_web_server.pojo.TaskRecordModel;
import com.example.atis_web_server.pojo.UserModel;
import com.example.atis_web_server.service.ITaskPoolService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.RequestBody;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Slf4j
@Service
public class TaskPoolService extends ServiceImpl<TaskPoolMapper, TaskPoolModel> implements ITaskPoolService {
    @Autowired
    private TaskPoolMapper taskPoolMapper;
    @Autowired
    private TaskRecordMapper taskRecordMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private TaskDefaultMapper taskDefaultMapper;
    @Autowired
    private ResourceLoader resourceLoader;
    private String generateTaskPrompt;
    private static final Logger logger = LoggerFactory.getLogger(TaskPoolService.class);

    public TaskPoolService(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    @PostConstruct
    public void init() throws IOException {
        // 在这里执行你的任务
        logger.info("任务池服务已初始化，执行启动时任务...");
        Resource resource = resourceLoader.getResource("classpath:generateTaskPrompt.md");
        generateTaskPrompt = new String(resource.getInputStream().readAllBytes());
    }

    /**
     * 初始化任务池 为用户添加默认任务
     *
     * @param uid 用户id
     */
    @Override
    public void initTaskPool(Long uid, Long doctorId) throws IOException {
        List<TaskPoolModel> taskPoolModels = new ArrayList<>();
        List<TaskDefaultModel> taskDefaultModelList = taskDefaultMapper.selectList(null);

        for (TaskDefaultModel taskDefaultModel : taskDefaultModelList) {
            TaskPoolModel taskPoolModel = new TaskPoolModel();
            taskPoolModel.setName(taskDefaultModel.getTopic());
            taskPoolModel.setUid(uid);
            taskPoolModel.setPrompt(taskDefaultModel.getHint());
            taskPoolModel.setRemark("默认任务");
            taskPoolModel.setTaskRecordId(-1);
            taskPoolModel.setDoctorId(doctorId);
            taskPoolModel.setDeleted(taskDefaultModel.getDeleted());

            taskPoolModels.add(taskPoolModel);
        }
        saveBatch(taskPoolModels);
    }

    /**
     * 更新任务池 更新用户的默认任务
     */
    @Override
    public void updateDefaultTaskPool() throws IOException {
        // 获取最新的TaskDefaultModel列表
        List<TaskDefaultModel> taskDefaultModelList = taskDefaultMapper.selectList(null);
        // 更新TaskPoolModel表中的数据
        for (TaskDefaultModel taskDefaultModel : taskDefaultModelList) {
            // 根据条件查询TaskPoolModel是否存在
            List<TaskPoolModel> existingTaskPoolModelList = selectTaskPoolModelByTopic(taskDefaultModel.getTopic());
            if (!existingTaskPoolModelList.isEmpty()) {
                for (TaskPoolModel existingTaskPoolModel : existingTaskPoolModelList) {
                    // 更新现有的TaskPoolModel
                    existingTaskPoolModel.setName(taskDefaultModel.getTopic());
                    existingTaskPoolModel.setPrompt(taskDefaultModel.getHint());
                    existingTaskPoolModel.setDeleted(taskDefaultModel.getDeleted());
                    saveOrUpdate(existingTaskPoolModel);
                }

            } else {
                // 如果不存在，则为每个用户新增TaskPoolModel
                List<TaskPoolModel> taskPoolModels = new ArrayList<>();
                List<UserModel> UserModelList = userMapper.selectList(null);
                for (UserModel UserModel : UserModelList) {
                    TaskPoolModel newTaskPoolModel = getTaskPoolModel(taskDefaultModel, UserModel);
                    taskPoolModels.add(newTaskPoolModel);
                }
                saveBatch(taskPoolModels);
            }
        }
    }

    @NotNull
    private static TaskPoolModel getTaskPoolModel(TaskDefaultModel taskDefaultModel, UserModel UserModel) {
        TaskPoolModel newTaskPoolModel = new TaskPoolModel();
        newTaskPoolModel.setUid(UserModel.getId());
        newTaskPoolModel.setDoctorId(UserModel.getDoctorId());
        newTaskPoolModel.setName(taskDefaultModel.getTopic());
        newTaskPoolModel.setPrompt(taskDefaultModel.getHint());
        newTaskPoolModel.setRemark("默认任务");
        newTaskPoolModel.setTaskRecordId(-1);
        newTaskPoolModel.setDeleted(taskDefaultModel.getDeleted());
        return newTaskPoolModel;
    }

    private List<TaskPoolModel> selectTaskPoolModelByTopic(String topic) {
        LambdaQueryWrapper<TaskPoolModel> queryWrapper = Wrappers.<TaskPoolModel>lambdaQuery()
                .eq(TaskPoolModel::getName, topic);
        return taskPoolMapper.selectList(queryWrapper);
    }

    @Override
    public List<TaskPoolModel> selectByUsed(Long uid, Boolean used) {
        LambdaQueryWrapper<TaskPoolModel> queryWrapper = Wrappers.<TaskPoolModel>lambdaQuery()
                .eq(TaskPoolModel::getUid, uid)
                .eq(TaskPoolModel::getUsed, used);
        return taskPoolMapper.selectList(queryWrapper);
    }

    /**
     * 由AI参考任务记录生成任务，加入任务池中
     * 1. 读取任务记录中的任务，选择used为false的任务
     * 2. 将所有选取的任务的used都设为true
     * 3. 将任务的记录和生成任务提示词，发送给AI ，由AI 生成新的话题
     * 4. 将新的话题与角色设定等组合为任务，保存到任务池表中 ；新任务注意加上taskRecordId
     */
    @Override
    public void generateTaskFromAI(Long uid, Long doctorId) throws Exception {
        List<TaskRecordModel> taskRecordModels = Db.lambdaQuery(TaskRecordModel.class)
//                .eq(TaskRecordModel::getDoctorId, doctorId)
                .eq(TaskRecordModel::getUid, uid)
                .eq(TaskRecordModel::getUsed, false)
                .eq(TaskRecordModel::getCompletion, true)
                .isNotNull(TaskRecordModel::getChatHistory)
                .list();

        for (TaskRecordModel taskRecordModel : taskRecordModels) {
            JSONObject json = JSONObject.parseObject(taskRecordModel.getChatHistory());

            var textHistory = json.getString("TextHistory");
            if (textHistory.isEmpty()) continue;

            RequestBody body = ChatGpt.buildChatRequestBody(
                    generateTaskPrompt,  // 系统角色设定
                    textHistory// 用户问题
            );
            String responseData = ChatGpt.PostMsg(body);
            if (responseData != null) {
                responseData = responseData.replace("```json", "").replace("```", "");
                JSONArray responseArray = JSONArray.parseArray(responseData);

                //  循环 将新的话题与任务组合，保存到任务池表中 ；新任务注意加上taskRecordId
                assert responseArray != null;
                for (Object o : responseArray) {
                    JSONObject jsonObject1 = JSONObject.parseObject(o.toString());
                    TaskPoolModel taskPoolModel = new TaskPoolModel();
                    taskPoolModel.setName((String) jsonObject1.get("topic"));
                    taskPoolModel.setPrompt((String) jsonObject1.get("context"));
                    taskPoolModel.setUid(uid);
                    taskPoolModel.setUsed(false);
                    taskPoolModel.setDeleted(false);
                    taskPoolModel.setTaskRecordId(Math.toIntExact(taskRecordModel.getId()));
                    taskPoolModel.setDoctorId(doctorId);
                    save(taskPoolModel);
                }

                taskRecordModel.setUsed(true);
                Db.saveOrUpdate(taskRecordModel);
            }
        }
    }


    /**
     * 构建 TaskPoolModel
     *
     * @return TaskPoolModel
     */
    @Override
    public TaskPoolModel toTaskPoolModel(ReqUpdateTaskPool req) {
        TaskPoolModel taskPoolModel = getById(req.getId());
        taskPoolModel.setName(req.getName());
        taskPoolModel.setPrompt(req.getPrompt());
        taskPoolModel.setUsed(req.getUsed());
        taskPoolModel.setRemark(req.getRemark());
        taskPoolModel.setDeleted(req.getDeleted());
        return taskPoolModel;
    }

    /**
     * 分页 复杂 查询r任务池
     *
     * @param req req
     * @return PageDTO<RespUserInfo>
     * TODO 是否有更好的联表查询？
     */
    @Override
    public PageDTO<RespTaskPoolInfo> queryTaskPoolPage(ReqTaskPoolInfo req, Long doctorId) {
        String taskName = req.getName();
        String username = req.getUsername();
        Short typeId = req.getTypeId();
        Boolean used = req.getUsed();
        Boolean deleted = req.getDeleted();
        Integer taskRecordId = req.getTaskRecordId();
        String doctorName = req.getDoctorName();

        // 1.构建分页条件
        Page<TaskPoolModel> page = req.toMpPage();
        // 2.分页查询
        // 2.1 先写为查两次的方法
        LambdaQueryWrapper<UserModel> wrapper = new LambdaQueryWrapper<UserModel>()
                .select(UserModel::getId, UserModel::getUsername)
                .eq(doctorId != null, UserModel::getDoctorId, doctorId)
                .like(username != null, UserModel::getUsername, username);

        List<UserModel> users = userMapper.selectList(wrapper);
        if (users.isEmpty()) {
            return new PageDTO<>();
        }
        List<Long> uidList = users.stream().map(UserModel::getId).collect(Collectors.toList());

        Page<TaskPoolModel> p = lambdaQuery()
                .eq(doctorId != null, TaskPoolModel::getDoctorId, doctorId)  // TODO admin 时，这个限制怎么办
                .in(username != null, TaskPoolModel::getUid, uidList)  // TODO admin 时，这个限制怎么办 这里如何查？
                .like(taskName != null, TaskPoolModel::getName, taskName)
                .eq(typeId != null, TaskPoolModel::getTypeId, typeId)
                .eq(used != null, TaskPoolModel::getUsed, used)
                .eq(deleted != null, TaskPoolModel::getDeleted, deleted)
                .eq(taskRecordId != null, TaskPoolModel::getTaskRecordId, taskRecordId)
//                .like(doctorName != null, TaskPoolModel::getDoctorName??, doctorNameReq)  //  TODO admin 这里如何查？
                .page(page);

        // 3. 查询用户
        List<Long> userIds = p.getRecords().stream().map(TaskPoolModel::getUid).collect(Collectors.toList());
        if (userIds.isEmpty()) {
            return new PageDTO<>();
        }
        // 3.2.根据用户id查询用户表
        List<UserModel> userModels = Db.lambdaQuery(UserModel.class).in(UserModel::getId, userIds).list();
//        // 3.3 查询用户名
//        List<String> usernames = userModels.stream().map(UserModel::getUsername).collect(Collectors.toList());

        // 4.封装VO结果
        return PageDTO.of(p, taskRecord -> {
            // 1.拷贝基础属性
            RespTaskPoolInfo vo = BeanUtil.copyProperties(taskRecord, RespTaskPoolInfo.class);
            // 2.设置用户名  // TODO 这么查，会不会有错误
            userModels.stream()
                    .filter(udm -> udm.getId().equals(taskRecord.getUid()))
                    .findFirst().ifPresent(userModel -> vo.setUsername(userModel.getUsername()));

            return vo;
        });
    }

    public void updateUsedById(Long id) {
        TaskPoolModel taskPoolModel = getById(id);
        taskPoolModel.setUsed(true);
        saveOrUpdate(taskPoolModel);
    }

    @Override
    public boolean isExistsTaskRPoolById(Long Id) {
        return !ObjUtil.isEmpty(taskPoolMapper.selectById(Id));
    }
}
