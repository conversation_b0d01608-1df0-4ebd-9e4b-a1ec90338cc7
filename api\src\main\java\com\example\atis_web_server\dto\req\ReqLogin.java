package com.example.atis_web_server.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "登录实体")
public class ReqLogin {
    @NotNull
    @ApiModelProperty("用户名")
    private String username;
    @NotNull
    @ApiModelProperty("密码")
    private String password;
}
