package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@TableName(ConstModelNameAttribute.Survey)
@Data
@NoArgsConstructor
@AllArgsConstructor
// 患者满意度调查问卷
public class SurveyModel {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("问卷记录ID")
    private Long id;
    @ApiModelProperty("患者ID")
    private Long uid;
    @ApiModelProperty("问卷完成时间")
    private LocalDateTime completeTime;
    @ApiModelProperty("调查问卷结果")
    private String survey;
}
