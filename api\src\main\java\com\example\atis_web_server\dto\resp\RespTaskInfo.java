package com.example.atis_web_server.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "任务响应实体-客户端")
public class RespTaskInfo {
    @ApiModelProperty("任务记录ID")
    private Long id;
    @ApiModelProperty("任务ID")
    private Long tid;
    @ApiModelProperty("任务名")
    private String name;
    @ApiModelProperty("第几天的任务")
    private Integer day;
    @ApiModelProperty("设定任务最大完成时间")
    private Integer time;
    @ApiModelProperty("任务类型")
    private Short typeId;
    @ApiModelProperty("大模型提示词prompt")
    private String prompt;
    @ApiModelProperty("GptName") // gpt-4o-mini   gpt-3.5-turbo  chatBaidu
    private String gptName;
}