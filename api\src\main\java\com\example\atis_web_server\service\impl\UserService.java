package com.example.atis_web_server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.example.atis_web_server.dto.query.PageDTO;
import com.example.atis_web_server.dto.req.BgReqUserInfo;
import com.example.atis_web_server.dto.req.ReqRegisterUser;
import com.example.atis_web_server.dto.req.ReqUpdateUser;
import com.example.atis_web_server.dto.resp.RespUserDetail;
import com.example.atis_web_server.dto.resp.RespUserInfo;
import com.example.atis_web_server.mapper.UserMapper;
import com.example.atis_web_server.pojo.DoctorModel;
import com.example.atis_web_server.pojo.UserDoctorModel;
import com.example.atis_web_server.pojo.UserLoginRecordModel;
import com.example.atis_web_server.pojo.UserModel;
import com.example.atis_web_server.service.IUserService;
import com.example.atis_web_server.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class UserService extends ServiceImpl<UserMapper, UserModel> implements IUserService {
    @Autowired
    private UserMapper userMapper;

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @PostConstruct
    public void init() {
        // 在这里执行你的任务
        logger.info("用户服务已初始化，执行启动时任务...");
        updateDaysForAllUsers();
    }

    @Override
    public UserModel getUserByName(String userName) {
        QueryWrapper<UserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserModel::getUsername, userName);
        return userMapper.selectOne(queryWrapper, false);
    }

    @Override
    public UserModel toUserModel(ReqUpdateUser req) {
        UserModel userModel = getById(req.getId());
        userModel.setName(req.getName());
        userModel.setGender(req.getGender());
        userModel.setBirthday(req.getBirthday());
        userModel.setPhone(req.getPhone());
//        userModel.setDoctorId(req.getDoctor_id());  // TODO 检查是否有admin权限，没有则不能更新此项
        return userModel;
    }

    @Override
    public void updateLoginRecord(UserLoginRecordModel req) {
        UserModel userModel = getById(req.getUid());
        userModel.setLoginTime(req.getLoginTime());
        saveOrUpdate(userModel);
    }

    @Override
    public boolean insert(UserModel userModel) {
        return userMapper.insert(userModel) > 0;
    }

    @Override
    public boolean isExistsUser(String userName) {
        QueryWrapper<UserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserModel::getUsername, userName);
        return userMapper.exists(queryWrapper);
    }

    @Override
    public boolean changePassword(Long uid, String password) {
        UserModel userModel = userMapper.selectById(uid);
        if (ObjUtil.isEmpty(userModel)) {
            return false;
        }
        userModel.setPassword(password);
        return userMapper.updateById(userModel) > 0;
    }

    @Override
    public boolean isExistsUserByUid(Long uid) {
        return !ObjUtil.isEmpty(userMapper.selectById(uid));
    }

    public Integer calculateAge(LocalDate birthday) {
        ZonedDateTime currentDateTime = ZonedDateTime.now(ZoneId.of("Asia/Shanghai"));
        LocalDate currentDate = currentDateTime.toLocalDate();
        return Period.between(birthday, currentDate).getYears();
    }

    // 客户端
    @Override
    public RespUserDetail getUserDetail(Long uid, String token) {
        RespUserDetail resp = getUserDetail(uid);
        //使用JWT工具类，生成身份令牌
        resp.setToken(token);
        return resp;
    }

    @Override
    public RespUserDetail getUserDetail(Long uid) {
        RespUserDetail resp = new RespUserDetail();
        UserModel userModel = getById(uid);
        resp.setUsername(userModel.getUsername());
        resp.setName(userModel.getName());
        resp.setUid(userModel.getId());
        resp.setGender(userModel.getGender());
        resp.setAge(calculateAge(userModel.getBirthday()));  // 动态生成年龄
        resp.setStar(99);
        resp.setUserInfo(userModel.getUserInfo());
        return resp;
    }

    // 后台
    @Override
    public RespUserInfo getUserInfo(Long uid) {
        RespUserInfo resp = new RespUserInfo();
        UserModel userModel = getById(uid);
        resp.setId(userModel.getId());
        resp.setUsername(userModel.getUsername());
        resp.setName(userModel.getName());
        resp.setGender(userModel.getGender());
        resp.setBirthday(userModel.getBirthday());
        resp.setAge(calculateAge(userModel.getBirthday()));  // 动态生成年龄
        resp.setPhone(userModel.getPhone());
        resp.setDeleted(userModel.getDeleted());

        return resp;
    }

    @Override
    public Long registerUser(ReqRegisterUser req, Long doctorUid) {
        UserModel userModel = new UserModel();

        userModel.setUsername(req.getUsername());
        userModel.setName(req.getName() != null ? req.getName() : req.getUsername());
        userModel.setGender(req.getGender() != null ? req.getGender() : true);
        userModel.setBirthday(req.getBirthday() != null ? req.getBirthday() : LocalDate.of(2018, 10, 1));
        userModel.setPhone(req.getPhone() != null ? req.getPhone() : "17300000000");

        userModel.setPassword(Utils.getSha256(Utils.DefaultPassword));
        userModel.setCreateTime(LocalDateTime.now());
        userModel.setLoginTime(LocalDateTime.now());
        userModel.setDeleted(false);
        userModel.setDoctorId(doctorUid);
        userModel.setDay(1);
        userModel.setUserInfo("");

        insert(userModel);

        return userModel.getId();
    }

    /**
     * 分页 复杂 查询用户
     *
     * @param req req
     * @return PageDTO<RespUserInfo>
     * TODO 是否有更好的联表查询？
     */
    @Override
    public PageDTO<RespUserInfo> queryUsersPage(BgReqUserInfo req, Long doctorId) {
        String username = req.getUsername();
        String name = req.getName();
        String doctorNameReq = req.getDoctorName();
        int age = req.getAge();
        // 计算年龄的范围
        LocalDate today = LocalDate.now();
        LocalDate startOfYear = today.minusYears(age);
        LocalDate endOfYear = startOfYear.plusDays(364); // 考虑闰年的情况
        int day = req.getDay();
        Boolean deleted = req.getDeleted();
        // 1.构建分页条件
        Page<UserModel> page = req.toMpPage();

        // 2.分页查询
        Page<UserModel> p = lambdaQuery()
                .like(UserModel::getDoctorId, doctorId)  // TODO admin 时，这个限制怎么办
                .like(username != null, UserModel::getUsername, username)
                .like(name != null, UserModel::getName, name)
//                .like(doctorName != null, UserModel::getDoctorName??, doctorNameReq)  //  TODO admin 这里如何查？
                .between(age != 0, UserModel::getBirthday, startOfYear, endOfYear)
                .eq(day != 0, UserModel::getDay, day)
                .eq(deleted != null, UserModel::getDeleted, deleted)
                .page(page);

        // 3. 查询用户
        List<Long> userIds = p.getRecords().stream().map(UserModel::getId).collect(Collectors.toList());
        if (userIds.isEmpty()) {
            return new PageDTO<>();
        }
        // 3.2.根据用户id查询用户医生表
        List<UserDoctorModel> userDoctorModels = Db.lambdaQuery(UserDoctorModel.class).in(UserDoctorModel::getUid, userIds).list();
        // 3.3 查询医生Id
        List<Long> doctorIds = userDoctorModels.stream().map(UserDoctorModel::getDoctorId).collect(Collectors.toList());
        if (doctorIds.isEmpty()) {
            return new PageDTO<>();
        }
        // 3.4.根据医生id查询医生表
        List<DoctorModel> doctorModels = Db.lambdaQuery(DoctorModel.class).in(DoctorModel::getId, doctorIds).list();
        // 3.5. 创建一个Map以快速查找医生姓名
        Map<Long, String> doctorNameMap = doctorModels.stream()
                .collect(Collectors.toMap(DoctorModel::getId, DoctorModel::getName));
        if (doctorNameMap.isEmpty()) {
            return new PageDTO<>();
        }

        // 4.封装VO结果
        return PageDTO.of(p, user -> {
            // 1.拷贝基础属性
            RespUserInfo vo = BeanUtil.copyProperties(user, RespUserInfo.class);
            // 2.处理特殊逻辑
            vo.setAge(calculateAge(vo.getBirthday()));

            // 3.1.设置医生
            // 根据UserDoctorModel找到对应的医生信息
            UserDoctorModel userDoctorModel = userDoctorModels.stream()
                    .filter(udm -> udm.getUid().equals(user.getId()))
                    .findFirst()
                    .orElse(null);
            if (userDoctorModel != null) {
                Long doctorIdx = userDoctorModel.getDoctorId();
                String doctorName = doctorNameMap.get(doctorIdx);
                vo.setDoctorId(doctorIdx);
                vo.setDoctorName(doctorName);
            }
            return vo;
        });
    }

    /**
     * 更新所有用户的注册天数
     */
    @Override
    public void updateDaysForAllUsers() {
        // 查询所有用户
        List<UserModel> users = userMapper.selectList(null);
// 获取当前的北京时间

        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));

        for (UserModel user : users) {
            // 计算注册天数，不考虑时间部分
            long days = ChronoUnit.DAYS.between(
                    user.getCreateTime().atZone(ZoneId.of("Asia/Shanghai")).toLocalDate(),
                    now.toLocalDate()) + 1; // 加1用于包括创建那一天
            // 设置注册天数
            user.setDay((int) days);
            // 更新用户
            updateById(user);
        }
    }

    /**
     * 更新用户个人信息
     */
    @Override
    public void updateUserInfoById(int uid, String addUserInfo) {
        UserModel userModel = getById(uid);
        userModel.setUserInfo(userModel.getUserInfo() + addUserInfo);
        saveOrUpdate(userModel);
    }
}
