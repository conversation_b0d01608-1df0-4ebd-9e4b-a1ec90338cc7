package com.example.atis_web_server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.example.atis_web_server.dto.resp.RespDataStatistics;
import com.example.atis_web_server.dto.resp.RespLoginRecord;
import com.example.atis_web_server.mapper.UserLoginRecordMapper;
import com.example.atis_web_server.pojo.UserLoginRecordModel;
import com.example.atis_web_server.pojo.UserModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class UserLoginRecordService {
    @Autowired
    private UserLoginRecordMapper userLoginRecordMapper;
    @Autowired
    private UserService userService;
    private static final Logger logger = LoggerFactory.getLogger(UserLoginRecordService.class);

    public boolean insertLoginRecord(UserLoginRecordModel userLoginRecordModel) {
        return userLoginRecordMapper.insert(userLoginRecordModel) > 0;
    }

    public List<RespLoginRecord> getLoginRecordAfterTime(long time) {
        QueryWrapper<UserLoginRecordModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(UserLoginRecordModel::getLoginTime, time);
        List<UserLoginRecordModel> userLoginRecordModels = userLoginRecordMapper.selectList(queryWrapper);
        List<RespLoginRecord> respLoginRecords = new ArrayList<>();
        for (UserLoginRecordModel userLoginRecordModel : userLoginRecordModels) {
            respLoginRecords.add(toResp(userLoginRecordModel));
        }
        return respLoginRecords;
    }

    private RespLoginRecord toResp(UserLoginRecordModel userLoginRecordModel) {
        RespLoginRecord respLoginRecord = new RespLoginRecord();
        UserModel userModel = userService.getById(userLoginRecordModel.getUid());
        respLoginRecord.setUsername(userModel.getUsername());
        respLoginRecord.setLoginTime(userLoginRecordModel.getLoginTime());
        respLoginRecord.setPlace(userLoginRecordModel.getPlace());
        respLoginRecord.setDevice(userLoginRecordModel.getDevice());
        return respLoginRecord;
    }

    public List<RespDataStatistics> getUserLoginData(int offsetTime) {
        return userLoginRecordMapper.getUserLoginData(offsetTime);
    }
}
