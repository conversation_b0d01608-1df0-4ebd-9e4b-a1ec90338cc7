{"summaryPrompt": "现在你是一个优秀的AI助手，请帮我总结下面所列对话的主要内容，并梳理对话中用户的一些特点或者爱好。文本内容为一名语言治疗师与一名接受治疗的儿童的对话。\n\n- **角色说明**：\n  - 角色为“assistant”的是语言治疗师。\n  - 角色为“user”的是接受治疗的儿童。\n\n- **输出要求**：\n  1. **内容（Context）**：对话的主要内容。\n  2. **用户信息（UserInfo）**：对话中用户的一些特点或者爱好。\n\n- **格式示例**：\n  {\n  \"Context\": \"对话围绕xx主题进行，xxxxx\",\n  \"UserInfo\": \"他喜欢的颜色是蓝色\"\n  }\n\n- **注意事项**：\n  - 输出中不要有性别、年龄、姓名的信息\n  - 如果用户没有说话或者从用户对话中提炼不出其特点或爱好，用户信息可以为空字符串\n\n请根据上述要求，进行输出。", "generateTaskPrompt": "请根据以下结构化文本，文本内容为一名语言治疗师与一名接受治疗的儿童的对话。请识别对话中儿童感兴趣的主题，并为语言治疗师提供适合儿童认知水平的引导性说明。\n\n- **角色说明**：\n  - 角色为“assistant”的是语言治疗师。\n  - 角色为“user”的是接受治疗的儿童。\n\n- **输出要求**：\n  1. **主题（Topic）**：识别儿童对话中感兴趣的事物或重点内容。\n  2. **引导说明（Context）**：提供与主题相关的、适合儿童理解的引导性说明。\n\n- **格式示例**：\n  [\n  {\n  \"topic\": \"动物\",\n  \"context\": \"讨论宠物的护理，动物的基本特征，以及儿童对动物的好奇心。\"\n  },\n  {\n  \"topic\": \"颜色\",\n  \"context\": \"探索不同颜色的名称和它们在日常生活中的应用。\"\n  }\n  ]\n\n- **注意事项**：\n  - 确保引导说明适合儿童的认知水平。\n  - 避免超出儿童的理解范围。\n  - 输出至少一条内容，至多不限数量。\n\n请根据上述要求，输出对话中的主题和相应的引导说明。"}