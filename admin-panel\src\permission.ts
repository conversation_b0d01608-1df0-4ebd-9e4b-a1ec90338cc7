import router from './router'
import { Message } from 'element-ui'
import { Route } from 'vue-router'
import { UserModule } from '@/store/modules/user'
import Cookies from 'js-cookie'

// 在每次路由跳转前执行的钩子函数
router.beforeEach(async (to: Route, from: Route, next: any) => {
  try {
    // 检查是否存在 token
    const token = Cookies.get('token')
    if (token) {
      // 如果存在 token，直接放行
      next()
    } else {
      // 如果不存在 token，检查目标路由是否需要认证
      if (!to.meta.notNeedAuth) {
        // 如果需要认证，跳转到登录页面
        next({ path: '/login' })
      } else {
        // 如果不需要认证，直接放行
        next()
      }
    }
  } catch (error) {
    console.error('路由跳转前发生错误:', error)
    next({ path: '/login' }) // 发生错误时，跳转到登录页面
  }
})

// 在每次路由跳转后执行的钩子函数
router.afterEach((to: Route) => {
  try {
    // 设置页面标题为路由元信息中的标题
    if (to.meta && to.meta.title) {
      document.title = to.meta.title
    } else {
      document.title = 'ATIS' // 默认标题
    }
  } catch (error) {
    console.error('路由跳转后设置标题时发生错误:', error)
  }
})
