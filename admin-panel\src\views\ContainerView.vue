<template>
  <div>
    <!-- 设置最外层容器高度为700px,在加上一个很细的边框 -->
    <el-container style="height: 900px; border: 1px solid #eee">
      <el-header style="font-size:40px;background-color:rgb(238, 241, 246)">
        ATIS 后台管理系统
        <el-button style="float: right;" type="danger" @click="handleLogout">退出登录</el-button>
      </el-header>
      <el-container>

        <el-aside width="220px" style="border: 1px solid #eee">
          <el-menu>
            <el-menu-item>
              <router-link to="/doctor">医生管理</router-link>
            </el-menu-item>
            <el-menu-item>
              <router-link to="/user">用户管理</router-link>
            </el-menu-item>
            <el-menu-item>
              <router-link to="/taskPool">任务池管理</router-link>
            </el-menu-item>
            <el-menu-item>
              <router-link to="/taskRecord">任务管理</router-link>
            </el-menu-item>
          </el-menu>
        </el-aside>

        <el-main>
          <router-view /> <!-- 这里是显示医生管理、用户管理、任务池管理、任务管理的地方 -->
        </el-main>

      </el-container>
    </el-container>
  </div>
</template>
  
  <script>
  import { UserModule } from '@/store/modules/user';

  export default {
    methods: {
      handleLogout() {
        UserModule.LogOut(); // 调用登出方法
        this.$router.push('/login'); // 跳转到登录页面
      }
    }
  }
  </script>
  
  <!-- <style>
  .el-header, .el-footer {
      background-color: #B3C0D1;
      color: #333;
      text-align: center;
      line-height: 60px;
    }
    
    .el-aside {
      background-color: #D3DCE6;
      color: #333;
      text-align: center;
      line-height: 200px;
    }
    
    .el-main {
      background-color: #E9EEF3;
      color: #333;
      text-align: center;
      line-height: 160px;
    }
    
    body > .el-container {
      margin-bottom: 40px;
    }
    
    .el-container:nth-child(5) .el-aside,
    .el-container:nth-child(6) .el-aside {
      line-height: 260px;
    }
    
    .el-container:nth-child(7) .el-aside {
      line-height: 320px;
    }
  </style> -->
  