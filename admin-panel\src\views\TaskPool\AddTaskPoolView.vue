<template>
    <div class="addBrand-container">
        <div class="container">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px">
                <!-- 修改操作才显示ID输入框 -->
                <el-form-item label="ID" prop="id" v-if="this.optType === 'update'">
                    <el-input v-model="ruleForm.id"></el-input>
                </el-form-item>
                <el-form-item label="名称" prop="name">
                    <el-input v-model="ruleForm.name"></el-input>
                </el-form-item>
                <el-form-item label="任务提示词" prop="prompt">
                    <el-input type="textarea" v-model="ruleForm.prompt"></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" v-model="ruleForm.remark"></el-input>
                </el-form-item>
                <el-form-item label="状态" prop="deleted">
                    <el-radio v-model="ruleForm.deleted" :label="true">已删除</el-radio>
                    <el-radio v-model="ruleForm.deleted" :label="false">未删除</el-radio>
                </el-form-item>
                <el-form-item label="是否使用" prop="used">
                    <el-radio v-model="ruleForm.used" :label="true">已使用</el-radio>
                    <el-radio v-model="ruleForm.used" :label="false">未使用</el-radio>
                </el-form-item>

                <div class="subBox">
                    <el-button type="primary" @click="submitForm('ruleForm', false)">保存</el-button>
                    <!-- 新增操作才显示 保存并继续添加任务 按钮 -->
                    <el-button v-if="this.optType === 'add'"
                    type="primary" @click="submitForm('ruleForm', true)">保存并继续添加任务</el-button>
                    <el-button @click="handleReturn">返回</el-button>
                </div>
            </el-form>
        </div>
    </div>
</template>
  
  
  <script>
import { addTaskPool, queryTaskPoolById, updateTaskPool} from '@/api/task'
  import router from '@/router';
  export default {
    data() {
        return {
            optType: '',//当前操作的类型，新增或者修改
            ruleForm: {
                id: '',
                name: '',
                deleted: '',
                remark: '',
                prompt: '',
                used: '',
            },
          rules: {
              id: [
                  { required: true, message: '请输入ID', trigger: 'blur' }
              ],
              deleted: [
                  { required: true, message: '请选择是否删除', trigger: 'blur' }
              ],
              remark: [
                  { required: true, message: '请输入备注', trigger: 'blur' }
              ],
              name: [
                  { required: true, message: '请输入任务名称', trigger: 'blur' }
              ],
              prompt: [
                  { required: true, message: '请输入任务提示词', trigger: 'blur' }
              ],
              used: [
                  { required: true, message: '请选择是否使用', trigger: 'blur' }
              ]


          }
      }
    },

    created() {
    //获取路由参数（id），如果有则为修改操作，否则为新增操作
    this.optType = this.$route.query.id ? 'update' : 'add'
      if (this.optType === 'update') {
        this.$message.info(this.$route.query.id)
          //修改操作，需要根据id查询用户信息用于 页面回显
          queryTaskPoolById(this.$route.query.id).then(res => {
              if (res.data.code === 200) {
                  // this.ruleForm = res.data.data  //将查询到的任务信息"赋值"给表单数据
                  this.ruleForm.id = res.data.data.id
                  this.ruleForm.deleted = res.data.data.deleted
                  this.ruleForm.remark = res.data.data.remark
                  this.ruleForm.name = res.data.data.name
                  this.ruleForm.prompt = res.data.data.prompt
                  this.ruleForm.used = res.data.data.used
              }
          })
    }
  },

    methods: {
        submitForm(formName, isContinue) {
            //进行表单校验
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 表单校验通过，发起Ajax请求，将数据提交到后端
                    if (this.optType === 'add') {//新增操作
                        addTaskPool(this.ruleForm).then((res) => {
                            if (res.data.code === 200) {
                                this.$message.success('任务添加成功！')

                                if (isContinue) {  //如果是保存并继续添加，则清空表单数据
                                    this.ruleForm = { //清空表单数据
                                        id: '',
                                        deleted: '',
                                        remark: '',
                                        name: '',
                                        prompt: '',
                                        used: '',
                                    }
                                } else {  //返回任务池列表
                                    this.$router.push('/taskPool')
                                }
                            } else {
                                this.$message.error(res.data.msg)
                            }
                        })
                    } else {//修改操作
                        updateTaskPool(this.ruleForm).then(res => {
                            if (res.data.code === 200) {
                                this.$message.success('任务池信息修改成功！');
                                // 返回到之前的页面并保持查询条件
                                // const searchParams = this.$route.query.searchParams || '';
                                // this.$router.push({ path: '/taskPool', query: { searchParams } });
                                this.handleReturn();
                            }
                        })
                    }
                }
      })
    },
    handleReturn() {
        // 返回到任务池列表页面，并传递当前页码和查询条件
        const queryParams = {
            pageNo: this.$route.query.pageNo || 1, // 获取当前页码，默认为1
            name: this.$route.query.name || '',
            username: this.$route.query.username || '',
            doctorName: this.$route.query.doctorName || '',
            typeId: this.$route.query.typeId || '',
            taskRecordId: this.$route.query.taskRecordId || '',
            used: this.$route.query.used || '',
            deleted: this.$route.query.deleted || ''
        };
        this.$router.push({ path: '/taskPool', query: queryParams });
    },
  }
}

  </script>
  
  
  
  
<style scoped>
.addBrand-container {
    margin: 30px;
    margin-top: 30px;
}

.HeadLable {
    background-color: transparent;
    margin-bottom: 0px;
    padding-left: 0px;
}

.container {
    position: relative;
    z-index: 1;
    background: #fff;
    padding: 30px;
    border-radius: 4px;
}

.subBox {
    padding-top: 30px;
    text-align: center;
    border-top: solid 1px #dcdcdc;
    /* Replaced $gray-5 with a hex color */
}

.el-form-item {
    margin-bottom: 29px;
}

.el-input {
    width: 293px;
}
</style>
  
    
