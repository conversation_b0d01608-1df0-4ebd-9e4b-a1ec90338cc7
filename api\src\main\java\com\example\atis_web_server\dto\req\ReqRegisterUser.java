package com.example.atis_web_server.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "用户注册实体")
public class ReqRegisterUser {
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("性别")
    private Boolean gender;
    @ApiModelProperty("生日")
    private LocalDate birthday;
    @ApiModelProperty("手机号")
    private String phone;
}
