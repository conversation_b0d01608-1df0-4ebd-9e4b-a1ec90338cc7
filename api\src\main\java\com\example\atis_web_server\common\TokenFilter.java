package com.example.atis_web_server.common;

import cn.hutool.json.JSONUtil;
import com.example.atis_web_server.service.impl.SessionManager;
import com.example.atis_web_server.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Order(2)
@WebFilter(filterName = "tokenFilter", urlPatterns = {"/client/*", "/Background/*"})
public class TokenFilter implements Filter {
    // 假设有一个用户会话管理服务，能够根据用户ID检查并注销旧的会话
    @Autowired
    private final SessionManager sessionManager;  // 这个类可以根据用户ID来管理会话状态
    private static final Logger logger = LoggerFactory.getLogger(TokenFilter.class);

    public TokenFilter(SessionManager sessionManager) {
        this.sessionManager = sessionManager;
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("Init TokenFilter");
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        logger.info("TokenFilter, URL:{}", request.getRequestURI());

        List<String> whiteListUrls = Arrays.asList("/Login", "/login", "/update", "/test");
        boolean inWhiteListUrl = whiteListUrls.stream().anyMatch(uri -> request.getRequestURI().contains(uri));
        if (inWhiteListUrl) {
            logger.info("URL in White List, skipping token check: {}", request.getRequestURI());
            filterChain.doFilter(servletRequest, servletResponse);
            return; // 记得返回，否则会继续执行下面的代码
        }

        try {
            String token = request.getHeader(Utils.TOKEN);
            if (!token.isEmpty() && Utils.isLegalToken(token)) {
                logger.info("legal URL:{}", request.getRequestURI());

                // 获取用户ID（假设Token中包含用户ID）
                Long userId = Utils.getUidByToken(token);

                // 检查是否有旧会话
                if (sessionManager.isSessionActive(userId, token) || request.getRequestURI().contains("/Background")) {
                    logger.info("Session is valid for user: {}", userId);
                    filterChain.doFilter(servletRequest, servletResponse);
                } else {
                    // 如果会话无效（即存在另一个地方登录），则禁止继续访问
                    logger.info("Session invalidated for user: {}", userId);
                    response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                    PrintWriter writer = response.getWriter();
                    writer.print(JSONUtil.toJsonStr(Result.error(401, "User logged in from another device")));
                }
            } else {
                logger.info("illegality URL:{}", request.getRequestURI());
                response.setStatus(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
                PrintWriter writer = response.getWriter();
                writer.print(JSONUtil.toJsonStr(Result.error(204, "Token Fail")));
            }
        } catch (Exception e) {
            logger.info("missing Token,or interface error:{}", request.getRequestURI());
            logger.error(e.getMessage());
            response.setStatus(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
            PrintWriter writer = response.getWriter();
            writer.print(JSONUtil.toJsonStr(Result.error(203, e.getMessage())));
        }
    }

    @Override
    public void destroy() {
        logger.info("Destroy TokenFilter");
    }
}
