import Vue from 'vue'
import VueRouter, { RouteConfig } from 'vue-router'

Vue.use(VueRouter)

const routes: Array<RouteConfig> = [
  {
    path: "/login",
    component: () => import("@/views/Login/LoginView.vue"),
    meta: { title: "ATIS", hidden: true, notNeedAuth: true }
  },



  {
    path: '/', 
    component: () => import('../views/ContainerView.vue'),
    redirect: 'user', //重定向
    //嵌套路由（子路由），对应的组件会展示在当前组件内部
    children: [//通过children属性指定子路由相关信息（path、component）
      {
        path: 'user', //用户管理
        name: 'user',
        component: () => import('../views/User/UserView.vue'),
        meta: { title: '用户管理', hidden: true, notNeedAuth: false}
      },
      {
        path: "/user/add", //添加、修改用户
        component: () => import("../views/User/AddUserView.vue"),
        meta: {title: "添加/修改用户", hidden: true, notNeedAuth: false}
      },
      {
        path: 'doctor', //医生管理
        name: 'doctor',
        component: () => import('../views/Doctor/DoctorView.vue'),
        meta: { title: '医生管理', hidden: true, notNeedAuth: false}
      },
      {
        path: "/doctor/add", //添加医生
        component: () => import("../views/Doctor/AddDoctorView.vue"),
        meta: {title: "添加医生", hidden: true, notNeedAuth: false}
      },
      {
        path: 'taskPool', //任务池管理
        name: 'taskPool',
        component: () => import('../views/TaskPool/TaskPoolView.vue'),
        meta: { title: '任务池管理', hidden: true, notNeedAuth: false}
      },
      {
        path: 'taskPool/add', //新增或修改任务池
        component: () => import('../views/TaskPool/AddTaskPoolView.vue'),
        meta: { title: '新增修改任务池', hidden: true, notNeedAuth: false}
      },

      {
        path: 'taskRecord', //任务管理
        name: 'taskRecord',
        component: () => import('../views/TaskRecord/TaskRecordView.vue'),
        meta: { title: '任务管理', hidden: true, notNeedAuth: false }
      },
      {
        path: 'taskRecord/add', //新增或修改任务池
        component: () => import('../views/TaskRecord/AddTaskRecordView.vue'),
        meta: { title: '新增修改任务记录', hidden: true, notNeedAuth: false }
      },
      {
        path: 'taskRecord/chatHistory', 
        component: () => import('../views/TaskRecord/ChatHistoryView.vue'),
        meta: { title: '聊天记录', hidden: true, notNeedAuth: false }
      },

    ]
  },

  {
    path: '/404',
    component: () => import('../views/404View.vue'),
    meta: { title: "ATIS", hidden: true, notNeedAuth: true }
  },
  {
    path: '*',
    redirect: '/404', //重定向
    meta: { hidden: true }
  }

]

const router = new VueRouter({
  routes
})

export default router
