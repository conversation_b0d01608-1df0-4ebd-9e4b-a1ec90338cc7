package com.example.atis_web_server.task;

import com.example.atis_web_server.service.impl.TaskRecordService;
import com.example.atis_web_server.service.impl.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Profile("release")
@Component
public class ScheduledTaskRelease {
    @Autowired
    private TaskRecordService taskRecordService;


    /**
     * 每天上午8点执行。
     * 1. 获取昨日用户完成任务情况
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void getYesterdayTaskData() throws Exception {
        taskRecordService.getYesterdayTaskData();
    }
}
