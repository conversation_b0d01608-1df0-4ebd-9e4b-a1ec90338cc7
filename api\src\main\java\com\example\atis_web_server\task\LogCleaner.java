package com.example.atis_web_server.task;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class LogCleaner {
    private static final String LOGS_DIR = "appLogs/logs/";

    @Scheduled(cron = "0 0 0 * * ?") // 每天凌晨执行
    public void cleanLogs() {
        try {
            // 获取日志目录下的所有文件
            List<Path> logFiles = Files.list(Paths.get(LOGS_DIR))
                    .filter(path -> path.toString().endsWith(".log")) // 过滤出日志文件
                    .collect(Collectors.toList());

            long now = System.currentTimeMillis();
            for (Path logFile : logFiles) {
                // 获取文件的最后修改时间
                long fileTime = Files.getLastModifiedTime(logFile).toMillis();
                // 如果文件超过30天未修改，则删除
                if (now - fileTime > 30L * 24 * 60 * 60 * 1000) { // 保留30天的日志
                    Files.delete(logFile);
                }
            }
        } catch (IOException e) {
            System.err.println("Error cleaning logs: " + e.getMessage());
        }
    }
}