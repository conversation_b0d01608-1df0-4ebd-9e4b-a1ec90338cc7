**任务**：从对话中提取结构化干预评估数据

## 输出格式
**只要json数据，不要代码块的语言标识符**
```json
{
  "Context": {
    "summary": "对话主要围绕[主题]展开，涉及[知识点1]、[知识点2]等内容",
    "user_profile": {
      "interests": {
        "兴趣点名称": {
          "frequency": "出现次数",
          "context": "具体表现描述"
        }
      },
      "interaction": {
        "engagement": "0-1评分",
        "focus_score": "0-1评分"
      },
      "knowledge": {
        "掌握知识点": ["知识点1", "知识点2"],
        "待强化领域": ["领域1", "领域2"]
      }
    }
  },
  "UserInfo": "自然语言描述版本（用于后续拼接）"
}
```

## 提取规则
1. **兴趣点识别**：
   - 至少识别1个兴趣领域。确实没有的，可为空。
   - 频率计算：同一主题连续出现≥2次对话计为1次

2. **交互特征评估**：
   - 对话积极性 = 用户主动发言次数 / 总对话轮次
   - 话题专注度 = 维持同一主题的轮次 / 总轮次

3. **知识水平评估**：
   - 掌握标准：能正确回答相关问题≥3次
   - 待强化标准：错误回答≥2次同一问题

## 自然语言生成规则
将user_profile转换为以下格式：
```
观察记录：
- 兴趣发展：在[领域]表现出持续兴趣（近期频率：X次）
- 互动特征：对话积极性（评分X/5），话题专注度（评分X/5）
- 知识掌握：熟练掌握[知识点]，需要加强[领域]
```

## 自闭症干预专项要求
1. 必须标注以下特征：
   - 眼神接触频率（如对话中可推断）
   - 情绪波动触发点
   - 特殊表达方式（如重复性语言）
2. 使用自闭症谱系障碍诊断标准相关术语
3. 避免使用负面评价词汇