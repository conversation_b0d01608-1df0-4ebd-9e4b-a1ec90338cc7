package com.example.atis_web_server.utils;

import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Random;

public class Utils {

    public static final String DefaultPassword = "123456";

    public static final String TOKEN = "Authorization";

    private static final String jwtKey = "oeRaQQ7Wo24sDqKSX3IM9ASGmdGPmkTd9jo1QTy4b7P9Ze5";

    public static String createToken(int uid) {
        DateTime now = DateTime.now();
        DateTime newTime = now.offsetNew(DateField.HOUR, 14);

        Map<String, Object> payload = new HashMap<String, Object>();
        //签发时间
        payload.put(JWTPayload.ISSUED_AT, now);
        //过期时间
        payload.put(JWTPayload.EXPIRES_AT, newTime);
        //生效时间
        payload.put(JWTPayload.NOT_BEFORE, now);
        //载荷
        payload.put("uid", uid);

        return JWTUtil.createToken(payload, jwtKey.getBytes());
    }

    public static boolean isLegalToken(String token) {
        cn.hutool.jwt.JWT jwt = JWTUtil.parseToken(token);

        boolean verifyKey = jwt.setKey(jwtKey.getBytes()).verify();

        boolean verifyTime = jwt.validate(0);
        return verifyKey && verifyTime;
    }

    public static Long getUidByToken(String token) {
        cn.hutool.jwt.JWT jwt = JWTUtil.parseToken(token);
        return (Long) (long) Integer.parseInt(jwt.getPayload("uid").toString());
    }

    public static String getSha256(String strToEncrypt) {
        try {
            // 创建一个MessageDigest实例，指定SHA-256算法
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 使用指定的字节数组更新摘要
            digest.update(strToEncrypt.getBytes(StandardCharsets.UTF_8));

            // 计算摘要
            byte[] hashBytes = digest.digest();

            // 将摘要转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            // 返回十六进制字符串
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    public static int getSum(String[] str) {
        int result = 0;
        for (int i = 0; i < str.length; i++) {
            result += (int) (Integer.parseInt(str[i]) * Math.pow(10, (str.length - i - 1)));
        }
        return result;
    }

    public static <T> T getRandomElement(List<T> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("List cannot be null or empty");
        }

        Random random = new Random();
        int randomIndex = random.nextInt(list.size()); // 随机生成一个索引
        return list.get(randomIndex); // 返回随机索引对应的元素
    }
}
