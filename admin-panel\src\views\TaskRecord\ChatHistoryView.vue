<template>
    <div>
        <el-card class="box-card">
            <span>聊天记录</span>
            <div class="chat-history-content" >
                <div class="chat-message">
                    <strong>任务标题:</strong> <span class="chat-content">{{ title }}</span>
                </div>
                <div class="chat-message">
                    <strong>开始时间:</strong> <span class="chat-content">{{ start_time }}</span>
                </div>
                <div v-for="(message, index) in chatHistory" :key="index" class="chat-message" :class="{'chat-message-user': message.Role === 'user'}">
                    <div class="chat-message-content">
                        <strong>{{ formatRole(message.Role) }}：</strong> <span class="chat-content">{{ message.Content }}</span> 
                    </div>
                </div>
            </div>
        </el-card>
        <div class="subBox">
            <!-- <el-button @click="() => this.$router.push({ path:'/taskRecord', query: { searchParams: JSON.stringify(this.$route.query.searchParams), pageNo: this.$route.query.pageNo || 1 }}) ">返回</el-button> -->
            <el-button @click="handleReturn">返回</el-button>
        </div>
    </div>
</template>

<script lang="ts">
import { queryTaskRecordById } from '@/api/task'

interface ChatMessage {
    Role: string;
    Content: string;
}

export default {
    methods: {
        formatRole(role: string) {
            if (role === 'system') return '系统';
            if (role === 'assistant') return 'AI';
            return '用户';
        },
        handleReturn() {
            // 返回到任务记录列表页面，并传递当前页码和查询条件
            const queryParams = {
                pageNo: this.$route.query.pageNo || 1, // 获取当前页码，默认为1
                name: this.$route.query.name || '',
                username: this.$route.query.username || '',
                doctorName: this.$route.query.doctorName || '',
                completion: this.$route.query.completion || '',
                day: this.$route.query.day || '',
                completeTime: this.$route.query.completeTime || '',
                used: this.$route.query.used || ''
            };
            this.$router.push({ path: '/taskRecord', query: queryParams });
        }    
    },
    data() {
        return {
            rawChatHistory: {}, // 原始聊天记录
            chatHistory: [] as ChatMessage[], // 聊天记录
            title: '',
            start_time: '',
        }
    },
    created() {
        this.$message.info("所选ID：" + this.$route.query.id)
        queryTaskRecordById(this.$route.query.id).then(res => {
            if (res.data.code === 200) {
                this.rawChatHistory = JSON.parse(res.data.data.chatHistory);  
                if (!this.rawChatHistory) {
                    alert("此任务未完成，没有聊天记录！");
                    this.$router.push('/taskRecord')
                    return;
                }
                this.chatHistory = this.rawChatHistory.TextHistory;  
                this.title = this.rawChatHistory.TaskDic.name;        

                const rawStartTime = this.rawChatHistory.StartTime;
                const year = rawStartTime.substring(0, 4);
                const month = rawStartTime.substring(4, 6);
                const day = rawStartTime.substring(6, 8);
                const hour = rawStartTime.substring(9, 11);
                const minute = rawStartTime.substring(11, 13);
                const second = rawStartTime.substring(13, 15);
                this.start_time = `${year}-${month}-${day} ${hour}:${minute}:${second}`;
            }
        })
    }
}
</script>

<style scoped>
.chat-history-content {
    max-height: 80vh; /* 使用视口高度的80% */
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #dcdfe6;
    border-radius: 5px;
    background-color: #f9f9f9;
}

.chat-message {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
}

.chat-message-content {
    display: flex;
    max-width: 70%;
}

.chat-message-user  {
    flex-direction: row; /* 用户消息时，反转布局方向 */
    background-color: cornsilk;
}

.chat-message strong {
    margin-bottom: 5px; /* 用户消息时，调整间距 */
    margin-right: 5px; /* 非用户消息时，调整间距 */
}

.chat-content {
    border: 1px solid #ccc;
    padding: 5px 10px;
    border-radius: 4px;
    word-wrap: break-word;
    background-color: #fff; /* 背景颜色 */
}

</style>