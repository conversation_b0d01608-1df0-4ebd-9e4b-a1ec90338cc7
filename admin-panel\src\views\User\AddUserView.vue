<template>
    <div class="addBrand-container">
        <div class="container">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px">
                <!-- 修改操作才显示ID输入框 -->
                <!-- <el-form-item label="ID" prop="id" v-if="this.optType === 'update'">
                    <el-input v-model="ruleForm.id"></el-input>
                </el-form-item> -->

                <el-form-item label="账号" prop="username">
                    <el-input v-model="ruleForm.username"></el-input>
                </el-form-item>
                <el-form-item label="用户姓名" prop="name">
                    <el-input v-model="ruleForm.name"></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="phone">
                    <el-input v-model="ruleForm.phone"></el-input>
                </el-form-item>
                <el-form-item label="性别" prop="gender">
                    <el-radio v-model="ruleForm.gender" :label="true">男</el-radio>
                    <el-radio v-model="ruleForm.gender" :label="false">女</el-radio>
                </el-form-item>

                <el-form-item label="出生日期" prop="birthday">
                    <el-date-picker v-model="ruleForm.birthday" type="date" placeholder="请选择出生日期" format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd">
                    </el-date-picker>
                </el-form-item>

                <!-- 修改操作才显示医生ID输入框 -->
                <!--<el-form-item v-if="this.optType === 'update'" label="医生" prop="doctor_id">  
                    <el-input v-model="ruleForm.doctor_id"></el-input>
                </el-form-item> -->

                <div class="subBox">
                    <el-button type="primary" @click="submitForm('ruleForm',false)">保存</el-button>
                    <!-- 新增操作才显示保存并继续添加用户按钮 -->
                    <el-button v-if="this.optType === 'add'" type="primary"  
                        @click="submitForm('ruleForm',true)">保存并继续添加用户 
                    </el-button>                                                             
                    <!-- <el-button @click="() => this.$router.push('/user')">返回</el-button> -->
                    <el-button @click="handleReturn()">返回</el-button>
                </div>
            </el-form>
        </div>
    </div>
</template>


<script>
import {addUser,queryUserById,updateUser} from '@/api/admin'
import router from '@/router';
export default {
  data() {
    return {
      optType: '',//当前操作的类型，新增或者修改
      ruleForm: {
        id: '',
        name: '',
        username: '',
        gender: true,
        phone: '',
        birthday:'',
        doctor_id: '',
      },
      rules: {
        name: [
            { required: true, message: '请输入用户姓名', trigger: 'blur' }
        ],
        username: [
            { required: true, message: '请输入用户账号', trigger: 'blur' }
        ],
        phone: [
          { required: true, trigger: 'blur',validator: (rule,value,callback) => {
              if(value === '' || (!/^1(3|4|5|6|7|8)\d{9}$/.test(value))){
                callback(new Error('请输入正确的手机号！'))
              }else{
                callback()
              }
            } 
          }
        ],
        birthday: [
            { required: true, message: '请输入出生日期', trigger: 'blur' }
        ],
        doctor_id: [
            {required: false, trigger: 'blur', validator: (rule, value, callback) => {
                if (value === '' || (!/^[0-9]\d*$/.test(value))) {
                    callback(new Error('医生ID不合法 必须为正整数'))
                } else {
                    callback()
                }
            }
            }
        ],
      }
    }
  },
  created() {
    //获取路由参数（id），如果有则为修改操作，否则为新增操作
    this.optType = this.$route.query.id ? 'update' : 'add'
      if (this.optType === 'update') {
          //修改操作，需要根据id查询用户信息用于 页面回显
          queryUserById(this.$route.query.id).then(res => {
              if (res.data.code === 200) {
                  // this.ruleForm = res.data.data  //将查询到的用户信息"赋值"给表单数据
                  this.ruleForm.id = res.data.data.id
                  this.ruleForm.name = res.data.data.name
                  this.ruleForm.username = res.data.data.username
                  this.ruleForm.gender = res.data.data.gender
                  this.ruleForm.phone = res.data.data.phone
                  this.ruleForm.birthday = res.data.data.birthday
                  this.ruleForm.doctor_id = res.data.data.doctorId
              }
          })
    }
  },
  methods: {
    submitForm(formName,isContinue) {
      //进行表单校验
      this.$refs[formName].validate((valid) => {
        if(valid) {
          //表单校验通过，发起Ajax请求，将数据提交到后端
          if(this.optType === 'add'){//新增操作
            addUser(this.ruleForm).then((res) => {
                if(res.data.code === 200){
                  this.$message.success('用户添加成功！')
                  if(isContinue){  //如果是保存并继续添加用户，则清空表单数据
                  this.ruleForm = { //清空表单数据
                      name: '',
                      username: '',
                      gender: true,
                      phone: '',
                      birthday: ''
                    }
                  }else {
                    // this.$router.push('/user')
                    this.handleReturn();
                  }
                }else {
                  this.$message.error(res.data.msg)
                }
              })
          }else{//修改操作
            updateUser(this.ruleForm).then(res => {
              if(res.data.code === 200){
                this.$message.success('用户信息修改成功！')
                //this.$router.push('/user')
                this.handleReturn();
              }
            })
          }
        }
      })
    },
    handleReturn() {
        // 返回到用户列表页面，并传递当前页码和查询条件
        const queryParams = {
            pageNo: this.$route.query.pageNo || 1, // 获取当前页码，默认为1
            name: this.$route.query.name || '',
            username: this.$route.query.username || '',
            age: this.$route.query.age || '',
            doctorName: this.$route.query.doctorName || '',
            day: this.$route.query.day || '',
            deleted: this.$route.query.deleted || ''
        };
        this.$router.push({ path: '/user', query: queryParams });
    },
  }
}
</script>




<style scoped>
.addBrand-container {
    margin: 30px;
    margin-top: 30px;
}

.HeadLable {
    background-color: transparent;
    margin-bottom: 0px;
    padding-left: 0px;
}

.container {
    position: relative;
    z-index: 1;
    background: #fff;
    padding: 30px;
    border-radius: 4px;
}

.subBox {
    padding-top: 30px;
    text-align: center;
    border-top: solid 1px #dcdcdc; /* Replaced $gray-5 with a hex color */
}

.el-form-item {
    margin-bottom: 29px;
}

.el-input {
    width: 293px;
}
</style>

  