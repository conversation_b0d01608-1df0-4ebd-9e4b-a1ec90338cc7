package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;

@TableName(ConstModelNameAttribute.AssetsUpdating)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
public class AssetsUpdatingModel {
    @TableId(type = IdType.AUTO) //mybatis-plus注解
    @ApiModelProperty("ID")
    private Long id;
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
    @ApiModelProperty("热更新网址")
    private String url;
}
