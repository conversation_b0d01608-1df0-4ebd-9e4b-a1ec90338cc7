package com.example.atis_web_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.atis_web_server.dto.req.ReqRegisterDoctor;
import com.example.atis_web_server.dto.req.ReqUpdateDoctor;
import com.example.atis_web_server.dto.resp.RespDoctorDetail;
import com.example.atis_web_server.pojo.DoctorModel;

import java.util.List;

public interface IDoctorService extends IService<DoctorModel> {


    List<DoctorModel> getUserList();

    RespDoctorDetail getUserDetail(Long uid);

    Long registerUser(ReqRegisterDoctor req);

    DoctorModel getUserByName(String userName);

    DoctorModel toUserModel(ReqUpdateDoctor req);

    void insert(DoctorModel doctorModel);

    boolean isExistsUser(String userName);

    boolean changePassword(int uid, String password);

    DoctorModel getUserByUid(Long uid);

    boolean isExistsUserByUid(Long uid);

    void delete(Long id);

    void setDeleted(Long id, boolean deleted);

    boolean updateUserInfo(DoctorModel doctorModel);
}
