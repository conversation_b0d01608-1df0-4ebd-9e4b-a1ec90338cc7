package com.example.atis_web_server.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "医生注册实体")
public class ReqRegisterDoctor {
    @NotNull
    @ApiModelProperty("医生用户名")
    private String username;
    @NotNull
    @ApiModelProperty("密码")
    private String password;
    @NotNull
    @ApiModelProperty("医生姓名")
    private String name;
}
