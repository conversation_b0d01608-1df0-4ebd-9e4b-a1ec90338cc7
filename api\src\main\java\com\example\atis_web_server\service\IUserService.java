package com.example.atis_web_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.atis_web_server.dto.query.PageDTO;
import com.example.atis_web_server.dto.req.BgReqUserInfo;
import com.example.atis_web_server.dto.req.ReqRegisterUser;
import com.example.atis_web_server.dto.req.ReqUpdateUser;
import com.example.atis_web_server.dto.resp.RespUserDetail;
import com.example.atis_web_server.dto.resp.RespUserInfo;
import com.example.atis_web_server.pojo.UserLoginRecordModel;
import com.example.atis_web_server.pojo.UserModel;

public interface IUserService extends IService<UserModel> {

    UserModel getUserByName(String userName);

    UserModel toUserModel(ReqUpdateUser req);

    void updateLoginRecord(UserLoginRecordModel req);

    boolean insert(UserModel userModel);

    boolean isExistsUser(String userName);

    boolean changePassword(Long uid, String password);

    boolean isExistsUserByUid(Long uid);

    // 客户端
    RespUserDetail getUserDetail(Long uid, String token);

    RespUserDetail getUserDetail(Long uid);

    // 后台
    RespUserInfo getUserInfo(Long uid);

    Long registerUser(ReqRegisterUser req, Long doctorUid);

    PageDTO<RespUserInfo> queryUsersPage(BgReqUserInfo req, Long doctorId);

    void updateDaysForAllUsers();

    void updateUserInfoById(int uid, String summary);
}
