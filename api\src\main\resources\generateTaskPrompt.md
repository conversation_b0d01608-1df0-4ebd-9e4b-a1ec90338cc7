**请根据对话内容生成1-3个儿童兴趣主题，每个主题需满足以下要求：**

## 输出格式
**只要json数据，不要代码块的语言标识符**
```json
[
  {
    "topic": "主题名称（具体名词）",
    "context": "包含2个探索方向的自然描述",
    "style": "引导探索式/比较分析式/故事续写式/多感官引导式/角色扮演式/渐进拆解式/问题解决式/模式发现式"
  }
]
```

## 生成规则
1. **主题提取**：
    - 必须基于对话中重复出现≥2次的具体名词
    - 优先选择与历史兴趣点相关的新角度
    - 示例：{"topic": "恐龙", "context": "...", "style": "..."}

2. **描述规范**：
    - 包含1个知识扩展点 + 1个互动建议
    - 使用指定风格的关键词：
      ```
      比较分析式 → 对比/比较/差异
      多感官引导式 → 触摸/聆听/观察
      角色扮演式 → 假装/扮演/作为XX
      ```

3. **风格选择**：
    - 根据儿童特征自动匹配（无需人工指定）
    - 允许多风格混合标记（如"比较分析式+渐进拆解式"）

4. **强制要求**：
    - 至少生成1个主题（对话信息不足时使用默认主题）
    - 每个context不超过35个汉字
    - 避免使用感叹号、问号等标点

## 示例输出
```json
[
  {
    "topic": "遥控车",
    "context": "对比沙滩和石板路上轮胎痕迹的差异，分步骤记录不同地面的行驶速度",
    "style": "比较分析式+渐进拆解式"
  },
  {
    "topic": "雨天",
    "context": "聆听不同雨声的录音样本，用手指描绘雨滴落下的轨迹",
    "style": "多感官引导式"
  }
]
```

## 默认主题（信息不足时使用）
```json
[
  {
    "topic": "今日发现",
    "context": "讨论今天遇到的有趣事物，分享最特别的形状或颜色",
    "style": "引导探索式"
  }
]
```