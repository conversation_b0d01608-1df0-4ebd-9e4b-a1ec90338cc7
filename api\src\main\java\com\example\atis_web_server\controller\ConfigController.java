package com.example.atis_web_server.controller;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.example.atis_web_server.common.Result;

import com.example.atis_web_server.dto.req.ReqUpdatingInfo;
import com.example.atis_web_server.dto.resp.RespUpdatingInfo;
import com.example.atis_web_server.service.impl.AssetsUpdatingService;
import com.example.atis_web_server.service.impl.VersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@Api(tags = "杂项管理接口")
@Slf4j
@RestController
@RequestMapping
@Component
public class ConfigController {
    @Autowired
    private AssetsUpdatingService assetsUpdatingService;
    @Autowired
    private VersionService versionService;

    private static final Logger logger = LoggerFactory.getLogger(ConfigController.class);

    @ApiOperation("版本检查")
    @PostMapping("/client/update")
    public Result update(@Valid @RequestBody ReqUpdatingInfo req) throws Exception {
        logger.info("***** update *****");
        if (!ObjUtil.isEmpty(req) && !StrUtil.isEmpty(req.getPlatform()) && !StrUtil.isEmpty(req.getVersion())) {
            String version = req.getVersion();
            String platform = req.getPlatform();
            logger.info("ForcedUpdating version = {}; platform = {}", version, platform);
            RespUpdatingInfo.AotUpdateInfo aot = new RespUpdatingInfo.AotUpdateInfo();
            aot.setForceUpdate(versionService.isForcedUpdating(version, platform));
            aot.setDownloadUrl(versionService.getURL(platform));

            RespUpdatingInfo.HotUpdateInfo hot = new RespUpdatingInfo.HotUpdateInfo();
            hot.setDownloadUrl(assetsUpdatingService.getInfo().getUrl());

            RespUpdatingInfo resp = new RespUpdatingInfo();
            resp.setAot(aot);
            resp.setHot(hot);

            return Result.success(resp);
        } else {
            return Result.error(404, "查询无果");
        }
    }

    @ApiOperation("测试")
    @GetMapping("/client/test")
    public Result test() {
        logger.info("***** test *****");
        Map<String, Object> map = new HashMap<>();
        map.put("test", "测试成功 2");
        return Result.success(map);
    }

    @ApiOperation("/")
    @GetMapping("/")
    public Result api() {
        logger.info("***** api *****");
        return Result.success();
    }
}
