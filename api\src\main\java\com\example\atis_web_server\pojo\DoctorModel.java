package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * 医生实体类
 */
@TableName(ConstModelNameAttribute.Doctor)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorModel {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("医生ID")
    private Long id;
    @ApiModelProperty("医生用户名")
    private String username;
    @ApiModelProperty("医生登录密码")
    private String password;
    @ApiModelProperty("医生姓名")
    private String name;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("登录时间")
    private LocalDateTime loginTime;
    @ApiModelProperty("是否逻辑删除")
    private Boolean deleted;
}
