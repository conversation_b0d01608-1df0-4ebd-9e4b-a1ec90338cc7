package com.example.atis_web_server.controller;

import com.example.atis_web_server.service.impl.SurveyService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 待删除  甲方说不要问卷功能了 TODO
 */
@Slf4j
@RestController
@Api(tags = "后台问卷管理接口")
@RequestMapping(value = "/Background/Survey")
public class BgSurveyController {
    @Autowired
    private SurveyService surveyService;


}
