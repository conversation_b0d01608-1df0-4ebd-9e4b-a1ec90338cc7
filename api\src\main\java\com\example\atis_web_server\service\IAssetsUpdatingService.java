package com.example.atis_web_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.atis_web_server.dto.query.PageDTO;
import com.example.atis_web_server.dto.req.ReqUpdateAssets;
import com.example.atis_web_server.pojo.AssetsUpdatingModel;

import java.util.List;


public interface IAssetsUpdatingService extends IService<AssetsUpdatingModel> {


    AssetsUpdatingModel getInfo();

    List<AssetsUpdatingModel> getAllInfo();

    boolean updateInfo(AssetsUpdatingModel assetsUpdatingModel);

    AssetsUpdatingModel toAssetsUpdatingModel(ReqUpdateAssets req);

    PageDTO<AssetsUpdatingModel> queryPage();
}
