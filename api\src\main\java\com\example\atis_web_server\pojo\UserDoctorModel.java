package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName(ConstModelNameAttribute.UserDoctor)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDoctorModel {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("主键ID")
    private Long id; // 主键ID
    @ApiModelProperty("患者ID")
    private Long uid; // 患者ID
    @ApiModelProperty("主治医生ID")
    private Long doctorId; // 主治医生ID

}
