
variables:
  MAVEN_CLI_OPTS: "-q -s settings.xml --batch-mode --fail-at-end -T 4"
  TEST_SSH_CMD: "ssh -o StrictHostKeyChecking=No root@$TEST_SERVER_HOST -p$TEST_SERVER_PORT"
  PROD_SSH_CMD: "ssh -o StrictHostKeyChecking=No root@$PROD_SERVER_HOST -p$PROD_SERVER_PORT"

stages:
  - build
  - deploy

## -- testing -----------------------------------------------------------

testing_api:
  stage: build
  image: maven:3.9-amazoncorretto-11-debian
  tags:
    - java-linux
  only:
    - testing
    - ci-test
  script:
    - cd api
    - mvn $MAVEN_CLI_OPTS clean package
    - mv target/atis_web_server-*.jar target/app.jar
    - eval "$(ssh-agent -s)"
    - echo "$TEST_SERVER_KEY" | ssh-add -
    - scp -o StrictHostKeyChecking=No -p -P $TEST_SERVER_PORT target/app.jar root@$TEST_SERVER_HOST:/bcidata/atis-testing/api/

testing_admin_panel:
  stage: build
  image: node:20.17
  tags:
    - java-linux
  only:
    - testing
    - ci-test
  script:
    - cd admin-panel
    - npm config set registry https://registry.npmmirror.com/
    - npm install --force
    - npm run build
    - tar -czvf dist.tar.gz dist
    - eval "$(ssh-agent -s)"
    - echo "$TEST_SERVER_KEY" | ssh-add -
    - scp -o StrictHostKeyChecking=No -p -P $TEST_SERVER_PORT dist.tar.gz root@$TEST_SERVER_HOST:/bcidata/atis-testing/admin-panel/

testing_deploy:
  stage: deploy
  image: alpine:latest
  tags:
    - java-linux
  only:
    - testing
    - ci-test
  before_script:
    - apk add --no-cache openssh
  script:
    - eval "$(ssh-agent -s)"
    - echo "$TEST_SERVER_KEY" | ssh-add -
    - $TEST_SSH_CMD "cd /bcidata/atis-testing/admin-panel/ && tar -xzvf dist.tar.gz"
    - $TEST_SSH_CMD "cd /bcidata/atis-testing && docker compose down && docker compose up -d"
    - $TEST_SSH_CMD "cd /bcidata/nginx && docker compose down && docker compose up -d"


## -- production -----------------------------------------------------------

prod_api:
  stage: build
  image: maven:3.9-amazoncorretto-11-debian
  tags:
    - java-linux
  only:
    - release
    - ci-test
  script:
    - cd api
    - mvn $MAVEN_CLI_OPTS clean package
    - mv target/atis_web_server-*.jar target/app.jar
    - eval "$(ssh-agent -s)"
    - echo "$PROD_SERVER_KEY" | ssh-add -
    - scp -o StrictHostKeyChecking=No -p -P $PROD_SERVER_PORT target/app.jar root@$PROD_SERVER_HOST:/bcidata/atis-release/api/

prod_admin_panel:
  stage: build
  image: node:20.17
  tags:
    - java-linux
  only:
    - release
    - ci-test
  script:
    - cd admin-panel
    - npm config set registry https://registry.npmmirror.com/
    - npm install --force
    - npm run build
    - tar -czvf dist.tar.gz dist
    - eval "$(ssh-agent -s)"
    - echo "$PROD_SERVER_KEY" | ssh-add -
    - scp -o StrictHostKeyChecking=No -p -P $PROD_SERVER_PORT dist.tar.gz root@$PROD_SERVER_HOST:/bcidata/atis-release/admin-panel/

prod_deploy:
  stage: deploy
  image: alpine:latest
  tags:
    - java-linux
  only:
    - release
    - ci-test
  before_script:
    - apk add --no-cache openssh
  script:
    - eval "$(ssh-agent -s)"
    - echo "$PROD_SERVER_KEY" | ssh-add -
    - $PROD_SSH_CMD "cd /bcidata/atis-release/admin-panel/ && tar -xzvf dist.tar.gz"
    - $PROD_SSH_CMD "cd /bcidata/atis-release && docker compose down && docker compose up -d"

