<template>
    <div class="addBrand-container">
        <div class="container">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px">
                <!-- 修改操作才显示ID输入框 -->
                <el-form-item label="ID" prop="id" v-if="this.optType === 'update'">
                    <el-input v-model="ruleForm.id"></el-input>
                </el-form-item>
                <el-form-item label="完成情况" prop="completion">
                    <el-radio v-model="ruleForm.completion" :label="true">已完成</el-radio>
                    <el-radio v-model="ruleForm.completion" :label="false">未完成</el-radio>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input v-model="ruleForm.remark"></el-input>
                </el-form-item>
                <el-form-item label="得分" prop="score">
                    <el-input v-model="ruleForm.score"></el-input>
                </el-form-item>
                <el-form-item label="任务时长" prop="time">
                    <el-input v-model="ruleForm.time"></el-input>单位：秒
                </el-form-item>
                <el-form-item label="是否使用" prop="used">
                    <el-radio v-model="ruleForm.used" :label="true">已使用</el-radio>
                    <el-radio v-model="ruleForm.used" :label="false">未使用</el-radio>
                </el-form-item>

                <div class="subBox">
                    <el-button type="primary" @click="submitForm('ruleForm', false)">保存</el-button>
                    <!-- 新增操作才显示 保存并继续添加任务 按钮 -->
                    <el-button v-if="this.optType === 'add'"
                    type="primary" @click="submitForm('ruleForm', true)">保存并继续添加任务</el-button>
                    <!-- <el-button @click="() => this.$router.push({ path:'/taskRecord', query: { searchParams: this.$route.query.searchParams, pageNo: this.$route.query.pageNo || 1 }})">返回</el-button> -->
                    <el-button @click="handleReturn">返回</el-button>
                </div>
            </el-form>
        </div>
    </div>
</template>
  
  
  <script>
import { addTaskRecord, queryTaskRecordById, updateTaskRecord} from '@/api/task'
  import router from '@/router';
  export default {
    data() {
        return {
            optType: '',//当前操作的类型，新增或者修改
            ruleForm: {
                id: '',
                completion: '',
                remark: '',
                score: '',
                time: '',
                used: '',
            },
          rules: {
              id: [
                  { required: true, message: '请输入ID', trigger: 'blur' }
              ],
              completion: [
                  { required: true, message: '请选择完成情况', trigger: 'blur' }
              ],
              remark: [
                  { required: true, message: '请输入备注', trigger: 'blur' }
              ],
              score: [
                  { required: true, message: '请输入得分', trigger: 'blur' }
              ],
              time: [
                  { required: true, message: '请输入任务时长', trigger: 'blur' }
              ],
              used: [
                  { required: true, message: '请选择是否使用', trigger: 'blur' }
              ]


          }
      }
    },

    created() {
    //获取路由参数（id），如果有则为修改操作，否则为新增操作
    this.optType = this.$route.query.id ? 'update' : 'add'
      if (this.optType === 'update') {
        this.$message.info("所选ID："+ this.$route.query.id)
          //修改操作，需要根据id查询用户信息用于 页面回显
          queryTaskRecordById(this.$route.query.id).then(res => {
              if (res.data.code === 200) {
                  // this.ruleForm = res.data.data  //将查询到的任务信息"赋值"给表单数据
                  this.ruleForm.id = res.data.data.id
                  this.ruleForm.completion = res.data.data.completion
                  this.ruleForm.remark = res.data.data.remark
                  this.ruleForm.score = res.data.data.score
                  this.ruleForm.time = res.data.data.time
                  this.ruleForm.used = res.data.data.used
              }
          })
    }
  },

    methods: {
        submitForm(formName, isContinue) {
            //进行表单校验
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    // 表单校验通过，发起Ajax请求，将数据提交到后端
                    if (this.optType === 'add') {//新增操作
                        addTaskRecord(this.ruleForm).then((res) => {
                            if (res.data.code === 200) {
                                this.$message.success('任务添加成功！')

                                if (isContinue) {  //如果是保存并继续添加，则清空表单数据
                                    this.ruleForm = { //清空表单数据
                                        id: '',
                                        completion: '',
                                        remark: '',
                                        score: '',
                                        time: '',
                                        used: '',
                                    }
                                } else {  //返回任务记录列表
                                    this.$router.push({ path: '/taskRecord', query: { searchParams: JSON.stringify(this.$route.query.searchParams), pageNo: this.$route.query.pageNo || 1 }})
                                }
                            } else {
                                this.$message.error(res.data.msg)
                            }
                        })
                    } else {//修改操作
                        updateTaskRecord(this.ruleForm).then(res => {
                            if (res.data.code === 200) {
                                this.$message.success('任务记录信息修改成功！')
                                // this.$router.push({ path: '/taskRecord', query: { searchParams: JSON.stringify(this.$route.query.searchParams), pageNo: this.$route.query.pageNo || 1 }})
                                this.handleReturn();
                            }
                        })
                    }
                }
      })
    },
    handleReturn() {
        // 返回到任务记录列表页面，并传递当前页码和查询条件
        const queryParams = {
            pageNo: this.$route.query.pageNo || 1, // 获取当前页码，默认为1
            name: this.$route.query.name || '',
            username: this.$route.query.username || '',
            doctorName: this.$route.query.doctorName || '',
            completion: this.$route.query.completion || '',
            day: this.$route.query.day || '',
            completeTime: this.$route.query.completeTime || '',
            used: this.$route.query.used || ''
        };
        this.$router.push({ path: '/taskRecord', query: queryParams });
    },
  }
}

  </script>
  
  
  
  
<style scoped>
.addBrand-container {
    margin: 30px;
    margin-top: 30px;
}

.HeadLable {
    background-color: transparent;
    margin-bottom: 0px;
    padding-left: 0px;
}

.container {
    position: relative;
    z-index: 1;
    background: #fff;
    padding: 30px;
    border-radius: 4px;
}

.subBox {
    padding-top: 30px;
    text-align: center;
    border-top: solid 1px #dcdcdc;
    /* Replaced $gray-5 with a hex color */
}

.el-form-item {
    margin-bottom: 29px;
}

.el-input {
    width: 293px;
}
</style>
  
    
