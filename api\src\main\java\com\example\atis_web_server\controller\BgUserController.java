package com.example.atis_web_server.controller;

import cn.hutool.core.util.ObjUtil;
import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.dto.req.BgReqUserInfo;
import com.example.atis_web_server.dto.req.ReqRegisterUser;
import com.example.atis_web_server.dto.req.ReqUpdateUser;
import com.example.atis_web_server.dto.req.ReqUserInfo;
import com.example.atis_web_server.pojo.UserModel;
import com.example.atis_web_server.service.impl.TaskPoolService;
import com.example.atis_web_server.service.impl.TaskRecordService;
import com.example.atis_web_server.service.impl.UserDoctorService;
import com.example.atis_web_server.service.impl.UserService;
import com.example.atis_web_server.utils.Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;

@Api(tags = "后台用户管理接口")
@RequestMapping(value = "/Background/users")
@Slf4j
@RestController
public class BgUserController {
    @Autowired
    private UserService userService;
    @Autowired
    private TaskPoolService taskPoolService;
    @Autowired
    private TaskRecordService taskRecordService;
    @Autowired
    private UserDoctorService userDoctorService;

    private static final Logger logger = LoggerFactory.getLogger(BgUserController.class);

    /**
     * 新增用户 TODO 优化重复代码
     */
    @ApiOperation("注册 用户")
    @PostMapping("/register")
    public Result register(@Valid @RequestBody ReqRegisterUser req, @RequestHeader(Utils.TOKEN) String token) throws Exception {
        logger.info("register user");
        Long doctorUid = Utils.getUidByToken(token);
        if (req.getUsername() != null) {
            String userName = req.getUsername();
            String name = req.getName();
            logger.info("Register : UserName = {};  name = {}", userName, name);
            if (!userName.matches("^[0-9a-zA-Z]+$")) {
                return Result.error(425, "无效请求, 用户名应为纯数字和字母");
            }
            if (userService.isExistsUser(userName)) {
                return Result.error(423, "用户已存在");
            } else {
                Long uid = userService.registerUser(req, doctorUid);
                if (userService.isExistsUser(userName)) {
                    taskPoolService.initTaskPool(uid, doctorUid);
                    userDoctorService.add(uid, doctorUid);
                    return Result.success(userService.getUserDetail(uid));
                } else {
                    return Result.error(425, "无效请求");
                }
            }
        } else {
            Long uid = userService.registerUser(req, doctorUid);
            UserModel userModel = userService.getById(uid);
            userModel.setUsername(uid.toString());  // 默认为uid
            userModel.setName(uid.toString());  // 默认为uid
            userModel.setGender(true);  // 默认男
            userModel.setBirthday(LocalDate.now());  // 默认生日为当天
            userModel.setPhone("");
            userService.saveOrUpdate(userModel);
            if (userService.isExistsUser(uid.toString())) {
                taskPoolService.initTaskPool(uid, doctorUid);
                userDoctorService.add(uid, doctorUid);
                return Result.success(userService.getUserDetail(uid));
            } else {
                return Result.error(425, "无效请求");
            }
        }
    }

    /**
     * 查询单个用户 主要是更新用户时，需要返回单个用户的部分信息
     */
    @ApiOperation("查询单个用户 - 后台")
    @PostMapping("/info")
    public Result getUserInfo(@Valid @RequestBody ReqUserInfo req) throws Exception {
        Long uid = req.getUid();
        if (userService.isExistsUserByUid(uid)) {
            return Result.success(userService.getUserInfo(uid));
        } else {
            return Result.error(404, "用户不存在");
        }
    }

    @ApiOperation("修改单个用户")
    @PostMapping("/update")
    public Result updateUserInfo(@Valid @RequestBody ReqUpdateUser req) throws Exception {
        if (ObjUtil.isEmpty(userService.getById(req.getId()))) {
            return Result.error(404, "用户不存在");
        }
//        String userModel_username = userModel1.getUsername();  // 注意：如果可以修改username 则此处需要判断 参考医生部分
        UserModel userModel = userService.toUserModel(req);
        boolean result = userService.saveOrUpdate(userModel);
        if (result) {
            return Result.success(userModel);
        } else {
            return Result.error(423, "更新失败");
        }
    }

    @ApiOperation("重置单个用户密码")
    @PostMapping("/resetPwd")
    public Result resetPwd(@Valid @RequestBody ReqUserInfo req) throws Exception {
        UserModel userModel = userService.getById(req.getUid());
        if (ObjUtil.isEmpty(userModel)) {
            return Result.error(404, "用户不存在");
        }
        userModel.setPassword(Utils.getSha256(Utils.DefaultPassword));
        boolean result = userService.saveOrUpdate(userModel);
        if (result) {
            return Result.success(userModel);
        } else {
            return Result.error(423, "更新失败");
        }
    }

    @ApiOperation("根据复杂条件分页查询用户接口")
    @GetMapping("/queryPage")
    public Result queryUsersPage(BgReqUserInfo query, @RequestHeader(Utils.TOKEN) String token) {
        Long doctorId = Utils.getUidByToken(token);
        return Result.success(userService.queryUsersPage(query, doctorId));
    }
}
