
package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@TableName(ConstModelNameAttribute.UserSession)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserSession {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("Token")
    private String token;
    @ApiModelProperty("标记会话是否有效")
    private Boolean isActive;
    @ApiModelProperty("创建时间")
    private String createdAt;
    @ApiModelProperty("更新时间")
    private String updatedAt;
}
