<template>
  <div>
    <!-- 搜索栏 -->
    <div class="tableBar">
      <label style="margin-right: 5px">任务名:</label>
      <el-input v-model="searchForm.name" placeholder="请输入任务名" style="width: 8%" />

      <label style="margin-right: 5px; margin-left: 20px">患者用户名:</label>
      <el-input v-model="searchForm.username" placeholder="请输入患者用户名" style="width: 8%" />

      <label style="margin-right: 5px; margin-left: 20px">医生姓名:</label>
      <el-input v-model="searchForm.doctorName" placeholder="请输入医生姓名" style="width: 8%" />

      <label style="margin-right: 5px; margin-left: 20px">任务类型:</label>
      <el-select v-model="searchForm.typeId" placeholder="选择任务类型" style="width: 8%">
        <el-option label="命名任务" :value="0"></el-option>
        <el-option label="提要求任务" :value="1"></el-option> 
        <el-option label="对话任务" :value="2"></el-option>
        <el-option label="全部"></el-option>
      </el-select>

      <label style="margin-right: 5px; margin-left: 20px">任务记录ID:</label>
      <el-input v-model="searchForm.taskRecordId" placeholder="输入任务记录ID" style="width: 8%" />

      <label style="margin-right: 5px; margin-left: 20px">是否完成过:</label>
      <el-select v-model="searchForm.used" placeholder="请选择" style="width: 6%">
        <el-option label="是" :value="true"></el-option>
        <el-option label="否" :value="false"></el-option>
        <el-option label="全部"></el-option>
      </el-select>

      <label style="margin-right: 5px; margin-left: 20px">逻辑删除:</label>
      <el-select v-model="searchForm.deleted" placeholder="请选择" style="width: 6%">
        <el-option label="否" :value="false"></el-option>
        <el-option label="是" :value="true"></el-option>
        <el-option label="全部"></el-option>
      </el-select>

      <!-- 搜索按钮 -->
      <el-button type="primary" style="margin-left: 20px" @click="pageQuery">查询</el-button>
      <br><br>
      <!-- 新增按钮 -->
      <!-- <el-button type="primary" style="float: right" @click="handleAddTask">+新增任务</el-button> -->
    </div>
    <br><br>

    <!-- 查询到的表格数据 -->
    <el-table v-bind:data="records" stripe style="width: 100%">
      <!-- 序号 -->
      <el-table-column label="序号" width="50">
                <template slot-scope="scope">
                    {{ (pageNo - 1) * pageSize + scope.$index + 1 }}
                </template>
      </el-table-column>

      <el-table-column prop="id" label="ID" width="100">
      </el-table-column>
      <el-table-column prop="name" label="任务名称" width="200">
      </el-table-column>
      <el-table-column prop="username" label="所属用户" width="100">
      </el-table-column>

      <el-table-column prop="typeId" label="任务类型" width="100">
        <template slot-scope="scope">
          {{ getTaskType(scope.row.typeId) }}
        </template>
      </el-table-column>
      <el-table-column prop="prompt" label="任务提示词">
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="100">
      </el-table-column>
      <el-table-column prop="used" label="是否完成" width="100">
        <template slot-scope="scope">
          {{ scope.row.used === true ? '已完成' : '未完成' }}
        </template>
      </el-table-column>
      <el-table-column prop="deleted" label="任务状态" width="100">
        <template slot-scope="scope">
          {{ scope.row.deleted === true ? '已禁用' : '启用中' }}
        </template>
      </el-table-column>
      <el-table-column prop="taskRecordId" label="任务来源" width="100">
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdateTask(scope.row)">修改</el-button>
          <el-button type="text" @click="handleStartOrStopTask(scope.row)">{{ scope.row.deleted === 1 ? '启用' : '禁用'
            }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <br><br>

    <!-- Pagination分页 -->
    <el-pagination class="pageList" @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="pageNo" :page-sizes="[10, 20, 30, 40, 50]" :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total" style="text-align: center;">
    </el-pagination>

  </div>
</template>

<script>
import axios from 'axios';
import {getTaskPoolList, addTaskPool, updateTaskPool, enableOrDisableTaskPool} from '@/api/task'
export default {
  data() {
    return {
      searchForm: {
        name: '', // 任务名
        username: '', // 患者用户名
        doctorName: '', // 医生姓名
        typeId: '', // 任务类型 0命名naming，1提要求answer，2对话任务chat
        taskRecordId: '', // 由哪个任务记录ID对应的聊天记录生成的：-1为默认任务
        used: '', // 是否完成过 对应的任务记录中的任务是否有被完成过的
        deleted: '', // 是否逻辑删除
      },
      pageNo: 1, // 当前页码
      pageSize: 10, // 每页显示数量
      total: 0, // 总数
      records: [], // 任务列表
    }
  },
  methods: {
    created() {
      this.pageQuery();
    },
    pageQuery() {
      // 请求参数
      const params = {};
      if (this.searchForm.name) params.name = this.searchForm.name;
      if (this.searchForm.username) params.username = this.searchForm.username;
      if (this.searchForm.doctorName) params.doctorName = this.searchForm.doctorName;
      if (this.searchForm.typeId) params.typeId = this.searchForm.typeId;
      if (this.searchForm.taskRecordId) params.taskRecordId = this.searchForm.taskRecordId;
      if (this.searchForm.used!== '') params.used = this.searchForm.used;
      if (this.searchForm.deleted!== '') params.deleted = this.searchForm.deleted;
      params.pageNo = this.pageNo;
      params.pageSize = this.pageSize;
      // this.$message.info('查询任务池参数：' + JSON.stringify(params));

      getTaskPoolList(params).then(resp => {
        if (resp.data.code == 200) {
          // this.$message.success('查询任务池成功');
          this.records = resp.data.data.list;
          this.total = resp.data.data.total;
          // this.$message. info('查询到的任务数量：' + this.records.length)
          // this.$message.info('总数：' + this.total)
        }
      }).catch(error => {
        this.$message.error("查询任务池失败" + error.message);
      })
    },
    handleAddTask() {
      // 保存当前查询条件到路由参数
      this.$router.push({ path: '/taskPool/add', query: {pageNo: this.pageNo, ...this.getQueryParams() } });
    },
    handleUpdateTask(row) {
      // 保存当前查询条件到路由参数
      this.$router.push({ path: '/taskPool/add', query: { id: row.id, pageNo: this.pageNo, ...this.getQueryParams()  } });
    },
    getQueryParams() {
      return {
        name: this.searchForm.name,
        username: this.searchForm.username,
        doctorName: this.searchForm.doctorName,
        typeId: this.searchForm.typeId,
        taskRecordId: this.searchForm.taskRecordId,
        used: this.searchForm.used,
        deleted: this.searchForm.deleted
      };
    },
    handleStartOrStopTask(row) {
      alert('启用或禁用任务', row);
      //弹出确认提示框
      // this.$confirm('确认要修改当前任务的状态吗?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   const p = {
      //     id: row.id,
      //     status: !row.deleted ? true : false
      //   }
      //   enableOrDisableTaskPool(p).then(res => {
      //     if (res.data.code === 200) {
      //       this.$message.success('任务状态修改成功！')
      //       this.pageQuery()  //重新查询数据
      //     }
      //   })
      // }).catch(() => {
      //   this.$message.info('已取消修改')
      // })
    },
    getTaskType(typeId) {
      switch (typeId) {
        case 0:
          return '命名任务';
        case 1:
          return '提要求任务';
        case 2:
          return '对话任务';
        default:
          return '未知任务';
      }
    },
    //pageSize发生变化时触发
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.pageQuery()
    },
    //pageNo发生变化时触发
    handleCurrentChange(pageNo) {
      this.pageNo = pageNo
      this.pageQuery()
    },

  },
  mounted() {
    // 处理路由参数，恢复查询条件
    const query = this.$route.query;

    if (query.pageNo) {
      this.pageNo = Number(query.pageNo);
    }
    this.searchForm.name = query.name || '';
    this.searchForm.username = query.username || '';
    this.searchForm.doctorName = query.doctorName || '';
    this.searchForm.typeId = query.typeId || '';
    this.searchForm.taskRecordId = query.taskRecordId || '';
    this.searchForm.used = query.used || '';
    this.searchForm.deleted = query.deleted || '';

    this.handleCurrentChange(this.pageNo);  // 两种更新方法 分页组件更新 都存在问题
  }
}
</script>

<style>
</style>