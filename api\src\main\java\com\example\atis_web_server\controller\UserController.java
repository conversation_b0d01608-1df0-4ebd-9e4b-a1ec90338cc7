package com.example.atis_web_server.controller;

import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.dto.req.ReqLogin;
import com.example.atis_web_server.dto.req.ReqUpdateUserPassword;
import com.example.atis_web_server.dto.req.ReqUserInfo;
import com.example.atis_web_server.pojo.UserLoginRecordModel;
import com.example.atis_web_server.pojo.UserModel;
import com.example.atis_web_server.service.impl.ConfigService;
import com.example.atis_web_server.service.impl.SessionManager;
import com.example.atis_web_server.service.impl.UserLoginRecordService;
import com.example.atis_web_server.service.impl.UserService;
import com.example.atis_web_server.utils.Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;

@Api(tags = "客户端用户管理接口")
@Slf4j
@RestController
@RequestMapping(value = "/client")
public class UserController {
    @Autowired
    private UserService userService;
    @Autowired
    private UserLoginRecordService userLoginRecordService;
    @Autowired
    private SessionManager sessionManager;
    @Autowired
    private ConfigService configService;

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    /**
     * 查询用户 根据username
     */
    @ApiOperation("查询用户信息")
    @PostMapping("/UserInfo/GetUser")
    public Result getUserInfo(@Valid @RequestBody ReqUserInfo req) throws Exception {
        Long uid = req.getUid();
        if (userService.isExistsUserByUid(uid)) {
            return Result.success(userService.getUserDetail(uid));
        } else {
            return Result.error(404, "查询无果");
        }
    }

    @ApiOperation("更改用户密码")
    @PostMapping(value = "/UserInfo/ModifyPassword")
    public Result ModifyPassword(@Valid @RequestBody ReqUpdateUserPassword req, @RequestHeader(Utils.TOKEN) String token) throws Exception {
        Long uid = Utils.getUidByToken(token);
        String password = req.getPassword();
        if (password.length() >= 6) {
            boolean result = userService.changePassword(uid, password);
            if (result) {
                return Result.success();
            } else {
                return Result.error(404, "用户不存在");
            }

        } else {
            return Result.error(423, "密码长度应大于6位");
        }
    }

    @ApiOperation("登录 用户")
    @PostMapping("/Login")
    public Result login(@Valid @RequestBody ReqLogin req) {
        String userName = req.getUsername();
        String password = req.getPassword();
        logger.info("login : userName = {};", userName);
        UserModel userModel = userService.getUserByName(userName);
        if (userModel == null) {
            return Result.error(404, "用户不存在");
        } else if (password.equals(userModel.getPassword())) {
            String token = Utils.createToken(Math.toIntExact(userModel.getId()));
            sessionManager.deleteSession(userModel.getId());
            sessionManager.createSession(userModel.getId(), token);
            return Result.success(userService.getUserDetail(userModel.getId(), token));
        } else {
            return Result.error(423, "密码错误");
        }
    }

    @ApiOperation("记录登录数据，并获取配置参数")
    @PostMapping("/UserInfo/recordLogin")
    public Result recordLogin(@RequestBody UserLoginRecordModel req, @RequestHeader(Utils.TOKEN) String token) {
        logger.info("***** RecodeLogin *****");
        Long uid = Utils.getUidByToken(token);
        req.setUid(uid);
        req.setLoginTime(LocalDateTime.now());
        userLoginRecordService.insertLoginRecord(req);
        userService.updateLoginRecord(req);
        return Result.success(configService.getInfo());
    }
}
