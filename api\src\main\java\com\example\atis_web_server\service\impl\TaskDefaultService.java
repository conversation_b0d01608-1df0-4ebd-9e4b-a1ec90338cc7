package com.example.atis_web_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.atis_web_server.mapper.TaskDefaultMapper;
import com.example.atis_web_server.pojo.TaskDefaultModel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;


@Slf4j
@Service
public class TaskDefaultService extends ServiceImpl<TaskDefaultMapper, TaskDefaultModel> {
    @Autowired
    private TaskDefaultMapper taskDefaultMapper;
    private static final Logger logger = LoggerFactory.getLogger(TaskDefaultService.class);

    public void addDefaultTask(String topic, String hint) throws IOException {
        TaskDefaultModel taskDefaultModel = new TaskDefaultModel();
        taskDefaultModel.setTopic(topic);
        taskDefaultModel.setHint(hint);
        taskDefaultModel.setDeleted(false);
        saveOrUpdate(taskDefaultModel);
    }

    //

    /**
     * 更新默认任务池中的任务
     * 不能修改topic，如果要修改topic，请标记旧的任务del为true，然后新建一个默认任务
     * TODO
     */
    public void updateTask(String hint, Boolean del, String remark) throws IOException {

    }

}
