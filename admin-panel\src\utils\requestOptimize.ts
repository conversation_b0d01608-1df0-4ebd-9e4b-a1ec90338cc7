import md5 from 'md5';

//根据请求的地址，方式，参数，统一计算出当前请求的md5值作为key
const getRequestKey = (config: { data: any; url: string; method: string; }) => {
    if (!config) {
        // 如果没有获取到请求的相关配置信息，根据时间戳生成
        return md5(new Date().toString());
    }

    const data = typeof config.data === 'string' ? config.data : JSON.stringify(config.data);
    // console.log(config,pending,config.url,md5(config.url + '&' + config.method + '&' + data),'config')
    return md5(config.url + '&' + config.method + '&' + data);
}

// 存储key值
const pending: { [key: string]: boolean } = {};
// 检查key值
const checkPending = (key: string | number) => !!pending[key];
// 删除key值
const removePending = (key: string | number) => {
    // console.log(key,'key')
    delete pending[key];
};

export {
    getRequestKey,
    pending,
    checkPending,
    removePending
}
