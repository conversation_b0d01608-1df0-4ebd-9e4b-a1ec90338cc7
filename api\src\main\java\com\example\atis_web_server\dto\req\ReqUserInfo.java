package com.example.atis_web_server.dto.req;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "单个ID查询实体")
public class ReqUserInfo {
    @NotNull
    @Min(0)
    @ApiModelProperty("id")
    private Long uid;
}
