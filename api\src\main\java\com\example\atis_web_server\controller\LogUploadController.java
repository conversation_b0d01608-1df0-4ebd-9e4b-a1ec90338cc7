package com.example.atis_web_server.controller;

import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.service.impl.UserService;
import com.example.atis_web_server.utils.Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@RestController
@Api(tags = "客户端日志上传管理接口")
@RequestMapping(value = "/client")
public class LogUploadController {

    @Autowired
    private UserService userService;

    private static final Logger logger = LoggerFactory.getLogger(LogUploadController.class);

    @ApiOperation("上传日志")
    @PostMapping(value = "/UpdateLog", consumes = "multipart/form-data")
    public Result uploadLog(
            @RequestHeader(Utils.TOKEN) String token,
            @RequestPart("file") @NotNull MultipartFile file
    ) throws Exception {
        try {
            // 获取文件名
            String fileName = file.getOriginalFilename();
            if (fileName == null || fileName.isEmpty()) {
                return Result.error(400, "文件名为空");
            }

            // 验证文件名安全性
            if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
                return Result.error(400, "非法的文件名");
            }

            // 获取文件数据
            byte[] fileData = file.getBytes();
//            if (fileData.length == 0) {
//                return Result.error(400, "文件数据为空");
//            }

            // 获取用户信息
            Long uid = Utils.getUidByToken(token);
            String name = userService.getById(uid).getName();

            // 构造保存路径
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String dateDir = dateFormatter.format(LocalDateTime.now());
            Path path = Paths.get("appLogs", String.format("%s-%s", uid.toString(), name), dateDir, fileName);

            // 创建目录
            Files.createDirectories(path.getParent());

            // 保存文件
            Files.write(path, fileData, StandardOpenOption.CREATE, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING);

            return Result.success();
        } catch (IOException e) {
            logger.error("日志文件上传失败: {}", e.getMessage());
            return Result.error(500, "服务器内部错误");
        } catch (Exception e) {
            logger.error("日志文件上传异常: {}", e.getMessage());
            return Result.error(400, "请求数据格式错误");
        }
    }
}