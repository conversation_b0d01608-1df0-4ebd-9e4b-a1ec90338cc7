package com.example.atis_web_server.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "请求更新软件版本的信息")
public class ReqUpdatingInfo {
    @NotNull
    @ApiModelProperty("id")
    private String version;
    @NotNull
    @ApiModelProperty("平台")
    private String platform;
}
