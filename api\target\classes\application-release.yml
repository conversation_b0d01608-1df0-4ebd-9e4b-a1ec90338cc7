#mybatis-plus:
#  global-config:
#    db-config:
#      logic-delete-field: deleted # 全局逻辑删除字段名
#      logic-delete-value: 1 # 逻辑已删除值
#      logic-not-delete-value: 0 # 逻辑未删除值

spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  flyway:
    enabled: true
    url: ${SPRING_DATASOURCE_URL}
    user: ${SPRING_DATASOURCE_USERNAME}
    password: ${SPRING_DATASOURCE_PASSWORD}
    baseline-version: 1.0.1
    baseline-description: "Initial_schema"
    locations: classpath:db/migration
    baseline-on-migrate: true  # 允许基线迁移
    validate-on-migrate: true
    clean-disabled: true
knife4j:
  enable: false
  openapi:
    title: 接口文档
    description: "接口文档"
    email: <EMAIL>
    concat: QJH
    version: v1.0.0
    group:
      default:
        group-name: default
        api-rule: package
        api-rule-resources:
          - com.example.atis_web_server.controller