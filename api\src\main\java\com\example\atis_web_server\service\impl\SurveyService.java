package com.example.atis_web_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.example.atis_web_server.mapper.SurveyMapper;
import com.example.atis_web_server.pojo.SurveyModel;

import com.example.atis_web_server.service.ISurveyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Service
public class SurveyService extends ServiceImpl<SurveyMapper, SurveyModel> implements ISurveyService {
    @Autowired
    private SurveyMapper surveyMapper;

    private static final Logger logger = LoggerFactory.getLogger(SurveyService.class);

    public void saveSurveyRecord(Long uid, String result) {
        SurveyModel surveyModel = new SurveyModel();
        surveyModel.setUid(uid);
        LocalDateTime now = LocalDateTime.now();
        surveyModel.setCompleteTime(now);
        surveyModel.setSurvey(result);
        save(surveyModel);
    }

    public Boolean getIsDoSurveyToday(Long uid) {
        LocalDateTime dateTime = surveyMapper.findMaxCompleteTimeByUid(uid);
        LocalDate today = LocalDate.now(); // 今天的日期
        if (dateTime == null) {
            return false;
        } else {
            return dateTime.toLocalDate().isEqual(today);
        }

    }

}
