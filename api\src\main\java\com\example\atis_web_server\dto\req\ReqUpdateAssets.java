package com.example.atis_web_server.dto.req;


import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data

public class ReqUpdateAssets {
    @NotNull
    private Long id;
    private Boolean active;
    private String url;
    private LocalDateTime update_time;
}
