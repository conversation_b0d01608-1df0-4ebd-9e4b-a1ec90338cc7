package com.example.atis_web_server.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "医生详细信息请求")
public class RespDoctorDetail {
    @ApiModelProperty("医生用户名")
    private String username;
    @ApiModelProperty("医生ID")
    private Long uid;
    @ApiModelProperty("姓名")
    private String name;
    //    private Boolean gender;
//    private Integer age;
    @ApiModelProperty("token")
    private String token;
}
