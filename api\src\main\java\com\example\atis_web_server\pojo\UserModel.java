package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@TableName(ConstModelNameAttribute.User)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserModel {
    @TableId(type = IdType.AUTO) //mybatis-plus注解
    @ApiModelProperty("用户id")
    private Long id;
    @ApiModelProperty("用户名: 病历号")
    private String username;
    @ApiModelProperty("密码")
    private String password;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("性别")
    private Boolean gender = true;
    @ApiModelProperty("生日")
    private LocalDate birthday = LocalDate.of(2018, 10, 1);
    //    @ApiModelProperty("年龄")
//    private Integer age;
    @ApiModelProperty("手机号")
    private String phone = "17300000000";
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("登录时间")
    private LocalDateTime loginTime;
    @ApiModelProperty("注册天数")
    private Integer day;
    @ApiModelProperty("医生ID")
    private Long doctorId;
    @ApiModelProperty("是否被逻辑删除")
    private Boolean deleted;
    @ApiModelProperty("个人信息：由聊天总结合并而成")
    private String userInfo;
}