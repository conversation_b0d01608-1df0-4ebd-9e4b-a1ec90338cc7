package com.example.atis_web_server.controller;

import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.dto.req.ReqUpdateVersion;
import com.example.atis_web_server.pojo.VersionModel;
import com.example.atis_web_server.service.impl.VersionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api(tags = "后台版本管理接口")
@RequestMapping(value = "/Background/Version")
@Slf4j
@RestController
public class BgVersionController {
    @Autowired
    private VersionService versionService;

    private static final Logger logger = LoggerFactory.getLogger(BgVersionController.class);

    @GetMapping(value = "/getVersionList")
    public Result getVersionList() throws Exception {
        return Result.success(versionService.queryPage());
    }

    @ApiOperation("更新版本信息")
    @PostMapping(value = "/updateVersion")
    public Result updateVersion(@Valid @RequestBody ReqUpdateVersion req) throws Exception {
        VersionModel versionModel = versionService.toVersionModelByReq(req);
        return versionService.updateVersion(versionModel) ? Result.success(versionModel) : Result.error(500, "请求异常");
    }
}
