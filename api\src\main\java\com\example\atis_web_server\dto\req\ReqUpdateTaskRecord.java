package com.example.atis_web_server.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "任务记录更新实体")
public class ReqUpdateTaskRecord {
    @NotNull
    @ApiModelProperty("任务id")
    private Long id;
    @ApiModelProperty("任务完成状态")
    private Boolean completion;
    @ApiModelProperty("任务得分")
    private Integer score;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("设定任务最大完成时间")
    private Integer time;
    @ApiModelProperty("是否被用于生成过新话题")
    private Boolean used;
}
