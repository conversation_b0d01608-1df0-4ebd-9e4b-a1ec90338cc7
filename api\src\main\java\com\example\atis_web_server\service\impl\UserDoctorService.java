package com.example.atis_web_server.service.impl;


import com.example.atis_web_server.mapper.UserDoctorMapper;
import com.example.atis_web_server.pojo.UserDoctorModel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class UserDoctorService {
    @Autowired
    private UserDoctorMapper userDoctorMapper;
    private static final Logger logger = LoggerFactory.getLogger(UserDoctorService.class);

    public void add(Long uid, Long doctorId) {
        UserDoctorModel userDoctorModel = new UserDoctorModel();
        userDoctorModel.setUid(uid);
        userDoctorModel.setDoctorId(doctorId);
        userDoctorMapper.insert(userDoctorModel);
    }
}
