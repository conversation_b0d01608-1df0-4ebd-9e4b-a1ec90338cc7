package com.example.atis_web_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.atis_web_server.dto.query.PageDTO;
import com.example.atis_web_server.dto.req.ReqTaskRecordInfo;
import com.example.atis_web_server.dto.req.ReqUpdateTaskRecord;
import com.example.atis_web_server.dto.resp.RespTaskInfo;
import com.example.atis_web_server.dto.resp.RespTaskRecordInfo;
import com.example.atis_web_server.pojo.TaskPoolModel;
import com.example.atis_web_server.pojo.TaskRecordModel;

import java.io.IOException;
import java.util.List;

public interface ITaskRecordService extends IService<TaskRecordModel> {
    void add(Integer day, TaskPoolModel taskPoolModel) throws Exception;

    PageDTO<RespTaskRecordInfo> queryTaskRecordPage(ReqTaskRecordInfo req, Long doctorId);

    TaskRecordModel toTaskRecordModel(ReqUpdateTaskRecord req);

    RespTaskInfo getByDay(Long userId) throws Exception;

    void updateCompletedById(Long id);

    void updateChatHistoryById(Integer id, String chatHistory, int uid, boolean exception) throws IOException;

    boolean isExistsTaskRecordById(Long Id);

    float getScheduleByUid(Long uid);

    void getYesterdayTaskData();

    void checkTaskNumForAllUsers() throws Exception;
}
