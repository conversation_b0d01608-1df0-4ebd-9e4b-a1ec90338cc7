package com.example.atis_web_server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.example.atis_web_server.dto.query.PageDTO;
import com.example.atis_web_server.dto.req.ReqUpdateVersion;
import com.example.atis_web_server.pojo.VersionModel;

import java.util.List;

public interface IVersionService extends IService<VersionModel> {


    List<VersionModel> getVersionList();

    VersionModel toVersionModelByReq(ReqUpdateVersion req);

    boolean updateVersion(VersionModel versionModel);

    boolean isForcedUpdating(String version, String platform);

    String getURL(String platform);

    PageDTO<VersionModel> queryPage();
}
