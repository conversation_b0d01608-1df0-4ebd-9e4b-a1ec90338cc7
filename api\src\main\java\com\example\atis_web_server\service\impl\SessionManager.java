package com.example.atis_web_server.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.atis_web_server.pojo.UserSession;
import com.example.atis_web_server.mapper.UserSessionMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SessionManager extends ServiceImpl<UserSessionMapper, UserSession> {

    @Autowired
    private UserSessionMapper userSessionMapper;

    private static final Logger logger = LoggerFactory.getLogger(SessionManager.class);

    // 检查用户会话是否有效
    public boolean isSessionActive(Long userId, String token) {
        QueryWrapper<UserSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(UserSession::getUserId, userId)
                .eq(UserSession::getToken, token)
                .eq(UserSession::getIsActive, true);
        UserSession userSession = userSessionMapper.selectOne(queryWrapper);
        return userSession != null;
    }

    // 使用户旧会话失效
    public void invalidateSession(Long userId) {
        QueryWrapper<UserSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserSession::getUserId, userId).eq(UserSession::getIsActive, true);
        UserSession userSession = userSessionMapper.selectOne(queryWrapper);
        if (userSession != null) {
            userSession.setIsActive(false);
            userSessionMapper.updateById(userSession);
        }
    }

    // 创建新的会话并更新数据库
    public void createSession(Long userId, String token) {
        // 使当前用户的会话无效
        invalidateSession(userId);

        // 创建新会话
        UserSession newSession = new UserSession();
        newSession.setUserId(userId);
        newSession.setToken(token);
        newSession.setIsActive(true);
        userSessionMapper.insert(newSession);
    }

    // 删除用户会话（用于退出登录时）
    public void deleteSession(Long userId) {
        QueryWrapper<UserSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserSession::getUserId, userId);
        userSessionMapper.delete(queryWrapper);
    }
}
