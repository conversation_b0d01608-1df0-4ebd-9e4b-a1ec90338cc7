package com.example.atis_web_server.dto.resp;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@Data
@ApiModel(description = "软件冷热更新信息响应")
public class RespUpdatingInfo {

    @ApiModelProperty("冷更新(AOT)信息")
    @JsonProperty("aot")
    private AotUpdateInfo aot;

    @ApiModelProperty("热更新(HOT)信息")
    @JsonProperty("hot")
    private HotUpdateInfo hot;

    @Data
    public static class AotUpdateInfo {
        @ApiModelProperty("是否强制更新")
        @JsonProperty("force_update")
        private boolean forceUpdate;

        @ApiModelProperty("安装包下载地址")
        @JsonProperty("download_url")
        private String downloadUrl;

    }

    @Data
    public static class HotUpdateInfo {
        @ApiModelProperty("热更新包下载地址")
        @JsonProperty("download_url")
        private String downloadUrl;

    }
}
