package com.example.atis_web_server.common;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

@Slf4j
public class ChatGpt {
    // 常量提取，便于统一维护
    private static final String MODEL_NAME = "gpt-4o-mini";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");
    private static final String ROLE_SYSTEM = "system";
    private static final String ROLE_USER = "user";
    private static final Logger logger = LoggerFactory.getLogger(ChatGpt.class);

    public static String PostMsg(RequestBody body) {
        OkHttpClient client = new OkHttpClient().newBuilder()
                .build();

        Request request = new Request.Builder()
                .url("https://api.chatanywhere.tech/v1/chat/completions")
                .method("POST", body)
                .addHeader("Authorization", "Bearer sk-KaIJwPbPyrfxr1OHyTfuwInUmZQZYqMuillBzVIKVT09rW77")  // sk
                .addHeader("User-Agent", "Apifox/1.0.0 (https://apifox.com)")
                .addHeader("Content-Type", "application/json")
                .build();
        // 发送请求并处理响应
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected code " + response);
            }
            // 获取响应体内容
            assert response.body() != null;
            String responseData = response.body().string();
//            logger.info("Response Data: {}", responseData);

            // 解析JSON响应
            JSONObject jsonResponse = JSONObject.parseObject(responseData);
            // 获取choices数组
            JSONArray choices = jsonResponse.getJSONArray("choices");
            // 假设我们只关心第一个choice
            if (!choices.isEmpty()) {
                JSONObject firstChoice = choices.getJSONObject(0);

                // 获取message对象
                JSONObject messageX = firstChoice.getJSONObject("message");

                // 提取content字段
                String contentX = messageX.getString("content");
                logger.info("Extracted content: {}", contentX);
                return contentX;
            } else {
                logger.error("ChatGpt.PostMsg: No choices found in the response.");
            }

        } catch (IOException e) {
            logger.error("ChatGpt.PostMsg:{1}", e);
        }
        return null;
    }

    public static RequestBody buildChatRequestBody(String systemContent, String userContent) {
        // 使用链式调用构建JSON结构
        JSONArray messages = new JSONArray();
        messages.add(createMessage(ROLE_SYSTEM, systemContent));
        messages.add(createMessage(ROLE_USER, userContent)); // 添加user部分

        JSONObject requestBody = new JSONObject();
        requestBody.put("model", MODEL_NAME);
        requestBody.put("messages", messages);

        return RequestBody.create(requestBody.toString(), JSON_MEDIA_TYPE);
    }

    // 封装消息创建方法
    private static JSONObject createMessage(String role, String content) {
        JSONObject message = new JSONObject();
        message.put("role", role);
        message.put("content", content);
        return message;

    }
}



