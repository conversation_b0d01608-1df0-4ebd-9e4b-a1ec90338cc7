package com.example.atis_web_server.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.LocalDateTime;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "登录记录请求实体")
public class RespLoginRecord {
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("登陆时间")
    private LocalDateTime loginTime;
    @ApiModelProperty("地点")
    private String place;
    @ApiModelProperty("设备")
    private String device;
}
