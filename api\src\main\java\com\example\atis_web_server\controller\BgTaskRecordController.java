package com.example.atis_web_server.controller;

import cn.hutool.core.util.ObjUtil;
import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.dto.req.ReqTaskRecordInfo;
import com.example.atis_web_server.dto.req.ReqUpdateTaskRecord;
import com.example.atis_web_server.dto.req.ReqUserInfo;
import com.example.atis_web_server.pojo.TaskRecordModel;
import com.example.atis_web_server.service.impl.TaskRecordService;
import com.example.atis_web_server.utils.Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@RestController
@Api(tags = "后台任务记录管理接口")
@RequestMapping(value = "/Background/TaskRecord")
public class BgTaskRecordController {
    @Autowired
    private TaskRecordService taskRecordService;

    private static final Logger logger = LoggerFactory.getLogger(BgTaskRecordController.class);

    @ApiOperation("修改单个任务池中的任务")
    @PostMapping("/update")
    public Object updateTaskRecordInfo(@Valid @RequestBody ReqUpdateTaskRecord req) throws Exception {
        if (ObjUtil.isEmpty(taskRecordService.getById(req.getId()))) {
            return Result.error(404, "任务不存在");
        }

        TaskRecordModel taskRecordModel = taskRecordService.toTaskRecordModel(req);
        boolean result = taskRecordService.updateById(taskRecordModel);
        if (result) {
            return Result.success(taskRecordModel);
        } else {
            return Result.error(423, "更新失败");
        }
    }

    @ApiOperation("根据复杂条件分页查询任务池接口")
    @GetMapping("/queryPage")
    public Result queryTaskRecordPage(ReqTaskRecordInfo query, @RequestHeader(Utils.TOKEN) String token) {
        Long doctorId = Utils.getUidByToken(token);
        return Result.success(taskRecordService.queryTaskRecordPage(query, doctorId));
    }

    /**
     * 查询单个任务记录 主要是更新任务记录时，需要返回单个任务记录的部分信息
     */
    @ApiOperation("查询单个任务记录 - 后台")
    @PostMapping("/info")
    public Result getTaskRecordInfo(@Valid @RequestBody ReqUserInfo req) throws Exception {
        Long id = req.getUid();
        if (taskRecordService.isExistsTaskRecordById(id)) {
            return Result.success(taskRecordService.getById(id));
        } else {
            return Result.error(404, "该任务记录不存在");
        }
    }

    /**
     * 检测所有用户任务池中数量是否足够
     */
    @ApiOperation("检测所有用户任务池中数量是否足够 - 后台")
    @PostMapping("/checkTaskNumForAllUsers")
    public Result checkTaskNumForAllUsers() throws Exception {
        taskRecordService.checkTaskNumForAllUsers();
        return Result.success();
    }

    /**
     * 获取昨天任务完成情况
     */
    @ApiOperation("获取昨天任务完成情况")
    @PostMapping("/getYesterdayTaskData")
    public Result getYesterdayTaskData() throws Exception {
        taskRecordService.getYesterdayTaskData();
        return Result.success();
    }
}
