package com.example.atis_web_server.task;

import com.example.atis_web_server.service.impl.TaskRecordService;
import com.example.atis_web_server.service.impl.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class ScheduledTask {
    @Autowired
    private UserService userService;
    @Autowired
    private TaskRecordService taskRecordService;

    private static final Logger logger = LoggerFactory.getLogger(ScheduledTask.class);

    @Scheduled(cron = "0 1 0 * * ?")
    public void updateUserDay() {
        try {
            logger.info("开始更新用户的 day 属性");
            userService.updateDaysForAllUsers();
            logger.info("用户 day 属性更新完成");
        } catch (Exception e) {
            logger.error("更新用户 day 属性时发生异常", e);
        }
    }

    @Scheduled(cron = "0 5 0 * * ?")
    public void updateAllTask() {
        try {
            logger.info("开始生成用户的每日任务记录");
            taskRecordService.checkTaskNumForAllUsers();
            logger.info("每日任务记录生成完成");
        } catch (Exception e) {
            logger.error("生成每日任务记录时发生异常", e);
        }
    }
}
