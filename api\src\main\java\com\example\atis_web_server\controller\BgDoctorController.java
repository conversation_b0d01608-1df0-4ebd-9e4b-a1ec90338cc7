package com.example.atis_web_server.controller;

import cn.hutool.core.util.ObjUtil;
import com.example.atis_web_server.dto.req.ReqLogin;
import com.example.atis_web_server.dto.req.ReqRegisterDoctor;
import com.example.atis_web_server.dto.req.ReqUpdateDoctor;
import com.example.atis_web_server.pojo.DoctorModel;
import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.service.impl.DoctorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Api(tags = "后台医生管理接口")
@RequestMapping(value = "/Background/doctors")
@Slf4j
@RestController
public class BgDoctorController {

    @Autowired
    private DoctorService doctorService;

    private static final Logger logger = LoggerFactory.getLogger(BgDoctorController.class);

//    /**
//     * 查询所有医生数据
//     */
//    @PostMapping(value = "/doctors/info")
//    public Result list() {
//        logger.info("查询所有医生数据");
//        List<DoctorModel> doctorList = doctorService.getUserList();
//        return Result.success(doctorList);
//    }

    /**
     * 新增医生数据
     */
    @ApiOperation("医生 注册")
    @PostMapping("/register")
    public Result register(@Valid @RequestBody ReqRegisterDoctor req) throws Exception {
        String userName = req.getUsername();
        String password = req.getPassword();
        String name = req.getName();
        logger.info("Register : UserName = {}; password  = {}; name = {}", userName, password, name);
        if (doctorService.isExistsUser(userName)) {
            return Result.error(423, "用户已存在");
        } else {
            Long uid = doctorService.registerUser(req);
            if (doctorService.isExistsUser(userName)) {
                return Result.success(doctorService.getUserDetail(uid));
            } else {
                return Result.error(425, "无效请求");
            }
        }
    }

    @ApiOperation("医生 登录")
    @PostMapping(value = "/login")
    public Result login(@Valid @RequestBody ReqLogin req) throws Exception {
        String userName = req.getUsername();
        String password = req.getPassword();
        logger.info("login : userName = {}; password  = {}", userName, password);
        DoctorModel userModel = doctorService.getUserByName(userName);
        if (userModel == null) {
            return Result.error(404, "用户或密码错误");
        } else if (password.equals(userModel.getPassword())) {
            return Result.success(doctorService.getUserDetail(userModel.getId()));
        } else {
            return Result.error(423, "用户或密码错误");
        }
    }

    @ApiOperation("根据Id获取医生信息")
    @PostMapping(value = "/info/{id}")
    public Result getUserInfo(@Valid @ApiParam("用户id") @PathVariable("id") Long id) throws Exception {
        if (doctorService.isExistsUserByUid(id)) {
            return Result.success(doctorService.getUserDetail(id));
        } else {
            return Result.error(404, "用户不存在");
        }
    }

    @ApiOperation("更新单个医生信息")
    @PostMapping(value = "/update")
    public Result updateUserInfo(@Valid @RequestBody ReqUpdateDoctor req) throws Exception {

        DoctorModel doctorModel1 = doctorService.getUserByUid(req.getId());
        if (ObjUtil.isEmpty(doctorModel1)) {
            return Result.error(404, "用户不存在");
        }
        String doctorModel1_username = doctorModel1.getUsername();

        if (doctorService.isExistsUser(req.getUsername()) && !req.getUsername().equals(doctorModel1_username)) {
            return Result.error(423, "该用户名已存在");
        } else {
            DoctorModel doctorModel = doctorService.toUserModel(req);
            boolean result = doctorService.updateUserInfo(doctorModel);
            if (result) {
                return Result.success(doctorModel);
            } else {
                return Result.error(423, "更新失败");
            }
        }
    }
}
