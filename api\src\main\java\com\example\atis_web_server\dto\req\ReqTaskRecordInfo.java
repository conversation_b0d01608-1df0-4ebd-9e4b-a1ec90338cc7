package com.example.atis_web_server.dto.req;

import com.example.atis_web_server.dto.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "任务记录查询条件实体")
public class ReqTaskRecordInfo extends PageQuery {
    @ApiModelProperty("任务名")
    private String name;
    @ApiModelProperty("用户名")
    private String username;
    @ApiModelProperty("任务完成状态")
    private Boolean completion;
    @ApiModelProperty("完成时间")
    private LocalDateTime completeTime;
    @ApiModelProperty("第几天的任务")
    private Integer day;
    @ApiModelProperty("是否被用于生成过新话题")
    private Boolean used;
    @ApiModelProperty("医生姓名")
    private String doctorName;  // admin
}