package com.example.atis_web_server.dto.req;


import com.example.atis_web_server.dto.query.PageQuery;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "任务池查询条件实体")
public class ReqTaskPoolInfo extends PageQuery {
    @ApiModelProperty("任务名")
    private String name = null;
    @ApiModelProperty("患者用户名:目前未设置如何查询")
    private String username;
    @ApiModelProperty("任务类型：0命名naming，1提要求answer，2对话任务chat")
    private Short typeId;
    @ApiModelProperty("任务是否被完成过：对应的任务记录中的任务是否有被完成过的")
    private Boolean used;
    @ApiModelProperty("是否逻辑删除")
    private Boolean deleted;
    @ApiModelProperty("由哪个任务记录对应的聊天记录生成的：-1为默认任务")
    private Integer taskRecordId;
    @ApiModelProperty("医生姓名")
    private String doctorName;  // admin
}
