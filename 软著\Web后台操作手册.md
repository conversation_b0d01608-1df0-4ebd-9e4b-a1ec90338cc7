# 儿童孤独症AI干预管理分析系统 [Web服务端] V1.0 - Web后台操作手册

## 1. 系统概述

### 1.1 系统简介
儿童孤独症AI干预管理分析系统Web后台是专为医生和管理人员设计的综合管理平台。系统基于Vue.js + Element UI开发，提供直观友好的操作界面，支持患者管理、任务管理、数据分析等核心功能。

### 1.2 系统特点
- **响应式设计**：支持不同屏幕尺寸的设备访问
- **权限管理**：基于角色的访问控制，确保数据安全
- **实时数据**：与后端API实时同步，数据更新及时
- **操作便捷**：简洁的界面设计，降低学习成本

### 1.3 浏览器要求
- **推荐浏览器**：Chrome 70+、Firefox 65+、Safari 12+、Edge 79+
- **分辨率要求**：最低1366×768，推荐1920×1080
- **网络要求**：稳定的互联网连接

## 2. 用户角色和权限说明

### 2.1 角色定义

#### 2.1.1 医生角色
**权限范围**：
- 管理自己负责的患者信息
- 创建和管理患者的任务
- 查看患者的任务完成情况
- 分析患者的治疗进度

**功能限制**：
- 不能查看其他医生的患者信息
- 不能修改系统配置参数
- 不能管理其他医生账户

#### 2.1.2 管理员角色（预留）
**权限范围**：
- 管理所有医生账户
- 查看系统整体数据
- 配置系统参数
- 管理系统版本

### 2.2 权限控制机制
- **登录验证**：所有功能需要先登录获取访问权限
- **Token认证**：使用JWT Token进行身份验证
- **路由守卫**：前端路由自动验证用户权限
- **数据隔离**：医生只能访问自己负责的患者数据

## 3. 系统登录

### 3.1 登录流程
1. 打开系统登录页面
2. 输入医生用户名和密码
3. 点击"登录"按钮
4. 系统验证成功后自动跳转到主界面

### 3.2 登录界面说明
**页面元素**：
- **用户名输入框**：输入医生账户的用户名
- **密码输入框**：输入对应的登录密码
- **登录按钮**：提交登录信息

**注意事项**：
- 用户名和密码区分大小写
- 连续登录失败可能会临时锁定账户

### 3.3 登录异常处理
**常见问题及解决方案**：
- **用户名或密码错误**：检查输入信息，联系管理员重置密码
- **网络连接异常**：检查网络连接，刷新页面重试
- **账户被锁定**：联系系统管理员解锁账户

## 4. 主界面介绍

### 4.1 界面布局
系统采用经典的后台管理布局：
- **顶部导航栏**：显示系统标题和用户操作区域
- **左侧菜单栏**：功能模块导航菜单
- **主内容区域**：显示当前选中功能的详细内容

### 4.2 顶部导航栏
**功能组件**：
- **系统标题**：显示"ATIS 后台管理系统"
- **退出登录按钮**：安全退出账户

### 4.3 左侧菜单栏
**主要菜单项**：
- **医生管理**：医生账户信息管理
- **用户管理**：患者信息管理
- **任务池管理**：任务模板管理
- **任务管理**：任务执行记录管理

## 5. 医生管理模块

### 5.1 功能概述
医生管理模块主要用于查看和管理当前登录医生的基本信息，包括个人资料查看、登录记录等。

### 5.2 医生信息查看
**操作步骤**：
1. 点击左侧菜单"医生管理"
2. 系统显示当前登录医生的详细信息
3. 可查看医生姓名、用户名、创建时间等基本信息

**显示内容**：
- 医生ID
- 用户名
- 医生姓名
- 账户创建时间
- 最后登录时间
- 账户状态

### 5.3 注意事项
- 医生只能查看自己的信息，不能查看其他医生
- 如需修改个人信息，请联系系统管理员
- 定期检查登录记录，确保账户安全

## 6. 用户管理模块

### 6.1 功能概述
用户管理模块是系统的核心功能之一，用于管理医生负责的患者信息，包括患者的基本信息维护、账户状态管理、新增患者等操作。

### 6.2 用户列表查看

#### 6.2.1 进入用户管理
**操作步骤**：
1. 点击左侧菜单"用户管理"
2. 系统显示当前医生负责的所有患者列表
3. 列表支持分页显示，默认每页显示10条记录

#### 6.2.2 列表信息说明
**表格列说明**：
- **用户ID**：系统自动生成的唯一标识
- **用户名**：患者的病历号，用于登录
- **姓名**：患者的真实姓名
- **性别**：男/女
- **年龄**：根据生日自动计算
- **手机号**：联系电话
- **医生姓名**：负责该患者的医生
- **注册天数**：患者使用系统的天数
- **创建时间**：账户创建时间
- **状态**：启用/禁用
- **操作**：启用/禁用按钮

### 6.3 用户查询功能

#### 6.3.1 查询条件
系统支持多种查询条件：
- **姓名**：支持模糊查询
- **用户名**：精确查询病历号
- **年龄**：按年龄筛选
- **医生姓名**：按负责医生筛选
- **注册天数**：按使用天数筛选
- **状态**：按启用/禁用状态筛选

#### 6.3.2 查询操作
**操作步骤**：
1. 在查询条件区域输入筛选条件
2. 点击"查询"按钮执行搜索
3. 系统显示符合条件的患者列表

### 6.4 新增用户

#### 6.4.1 新增操作流程
**操作步骤**：
1. 在用户管理页面点击"新增用户"按钮
2. 系统跳转到用户新增页面
3. 填写患者基本信息
4. 点击"保存"按钮提交信息
5. 系统验证通过后创建新用户

#### 6.4.2 必填信息
**基本信息**：
- **用户名**：患者病历号（必填，系统内唯一）
- **姓名**：患者真实姓名（必填）
- **性别**：选择男或女（必填）
- **生日**：患者出生日期（必填）
- **手机号**：联系电话（可选）

#### 6.4.3 注意事项
- 用户名（病历号）必须唯一，不能重复
- 系统会自动为新用户生成默认密码
- 新用户创建后默认为启用状态
- 患者年龄会根据生日自动计算

### 6.5 用户状态管理

#### 6.5.1 启用/禁用用户
**操作步骤**：
1. 在用户列表中找到目标患者
2. 点击操作列的"启用"或"禁用"按钮
3. 系统弹出确认对话框
4. 点击"确定"执行状态变更
5. 系统更新用户状态并刷新列表

#### 6.5.2 状态说明
- **启用状态**：患者可以正常登录和使用移动端APP
- **禁用状态**：患者无法登录系统，但数据保留
- **管理员账户**：系统管理员账户不能被禁用

### 6.6 分页功能
**分页控制**：
- **每页显示数量**：可选择10、20、30、40、50条记录
- **页码跳转**：支持直接跳转到指定页码
- **总数显示**：显示符合条件的记录总数
- **上一页/下一页**：快速翻页操作

## 7. 任务池管理模块

### 7.1 功能概述
任务池管理模块用于创建和管理孤独症干预任务的模板。医生可以为不同的患者创建个性化的任务模板，系统会根据这些模板自动为患者生成每日任务。

### 7.2 任务池列表查看

#### 7.2.1 列表信息
**表格列说明**：
- **任务ID**：系统自动生成的任务标识
- **任务名称**：任务的描述性名称
- **患者用户名**：任务对应的患者病历号
- **医生姓名**：创建任务的医生
- **任务类型**：0-命名任务，1-提要求任务，2-对话任务
- **是否完成过**：该任务是否被患者完成过
- **任务记录ID**：关联的任务记录ID（-1表示默认任务）
- **状态**：启用/禁用
- **操作**：编辑、删除等操作按钮

### 7.3 任务查询功能
**支持的查询条件**：
- **任务名称**：模糊查询任务名称
- **患者用户名**：按患者筛选任务
- **医生姓名**：按创建医生筛选
- **任务类型**：按任务类型筛选
- **完成状态**：按是否完成过筛选
- **任务状态**：按启用/禁用状态筛选

### 7.4 任务编辑和删除

#### 7.4.1 编辑任务
**操作步骤**：
1. 在任务列表中点击"编辑"按钮
2. 修改任务信息
3. 保存更改

#### 7.4.2 删除任务
**操作步骤**：
1. 点击"删除"按钮
2. 确认删除操作
3. 系统执行逻辑删除（数据保留但不显示）

## 8. 任务管理模块

### 8.1 功能概述
任务管理模块用于查看和分析患者的任务执行记录，包括任务完成情况、聊天记录、得分统计等信息。

### 8.2 任务记录列表

#### 8.2.1 列表信息
**表格列说明**：
- **记录ID**：任务记录的唯一标识
- **任务ID**：对应的任务池ID
- **患者用户名**：执行任务的患者
- **完成状态**：已完成/未完成
- **完成时间**：任务完成的具体时间
- **任务得分**：系统评估的任务得分
- **任务天数**：第几天的任务
- **设定时长**：任务的预设完成时间
- **聊天总结**：AI生成的对话总结
- **操作**：查看详情等操作

### 8.3 任务记录查询
**查询条件**：
- **患者用户名**：按患者筛选记录
- **完成状态**：按完成情况筛选
- **任务天数**：按任务天数筛选
- **完成时间范围**：按时间段筛选

### 8.4 任务详情查看

#### 8.4.1 查看聊天记录
**操作步骤**：
1. 点击任务记录的"查看详情"按钮
2. 系统显示完整的聊天记录
3. 可查看患者与AI的对话内容
4. 查看系统生成的对话总结

#### 8.4.2 任务分析
**分析内容**：
- **完成时长**：患者实际完成任务的时间
- **对话轮次**：患者与AI的交互次数
- **任务得分**：基于对话质量的评分
- **进步情况**：与历史记录的对比分析

## 9. 附录

### 9.1 浏览器兼容性
**推荐浏览器版本**：
- **Chrome**：70及以上版本
- **Firefox**：65及以上版本
- **Safari**：12及以上版本
- **Edge**：79及以上版本

**不支持的浏览器**：
- 过旧版本的移动浏览器

### 9.2 系统要求
**硬件要求**：
- **内存**：最低4GB，推荐8GB以上
- **硬盘空间**：至少1GB可用空间
- **网络带宽**：最低2Mbps，推荐10Mbps以上
- **屏幕分辨率**：最低1366×768，推荐1920×1080

**软件要求**：
- **操作系统**：Windows 10/11、macOS 10.14+、Linux（主流发行版）
- **浏览器**：见浏览器兼容性列表
- **网络协议**：支持HTTPS/TLS 1.2+

### 9.3 术语解释
**专业术语**：
- **孤独症**：自闭症谱系障碍，影响社交和沟通能力的发育障碍
- **AI干预**：使用人工智能技术进行治疗干预
- **任务池**：预设的治疗任务模板集合
- **任务记录**：患者完成任务的详细记录
- **聊天历史**：患者与AI对话的完整记录
- **Token**：用于身份验证的安全令牌
- **API**：应用程序编程接口
- **JWT**：JSON Web Token，一种安全令牌格式

本操作手册详细介绍了儿童孤独症AI干预管理分析系统Web后台的所有功能模块和操作流程，为医生和管理人员提供了完整的使用指导。在实际使用过程中，如遇到问题请及时联系技术支持团队。
