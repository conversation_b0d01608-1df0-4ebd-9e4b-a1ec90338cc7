package com.example.atis_web_server.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "更新软件版本")
public class ReqUpdateVersion {
    @NotNull
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("平台")
    @NotNull
    private String platform;
    @ApiModelProperty("版本")
    @NotNull
    private String version;
    @ApiModelProperty("软件更新地址")
    @NotNull
    private String url;
}
