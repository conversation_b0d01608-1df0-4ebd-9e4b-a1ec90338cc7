package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@TableName(ConstModelNameAttribute.TaskDefault)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskDefaultModel {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty("默认任务id")
    private Long id;
    @ApiModelProperty("任务名称")
    private String topic;
    @ApiModelProperty("任务提示")
    private String hint;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("是否逻辑删除")
    private Boolean deleted = false;
}
