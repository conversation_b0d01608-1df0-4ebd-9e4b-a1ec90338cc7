package com.example.atis_web_server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.example.atis_web_server.pojo.SurveyModel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;


public interface SurveyMapper extends BaseMapper<SurveyModel> {
    @Select("SELECT MAX(complete_time) FROM survey WHERE uid = #{uid}")
    LocalDateTime findMaxCompleteTimeByUid(@Param("uid") Long uid);
}
