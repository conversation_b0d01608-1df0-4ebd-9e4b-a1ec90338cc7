package com.example.atis_web_server.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.atis_web_server.dto.req.ReqRegisterDoctor;
import com.example.atis_web_server.dto.req.ReqUpdateDoctor;
import com.example.atis_web_server.dto.resp.RespDoctorDetail;
import com.example.atis_web_server.mapper.DoctorMapper;
import com.example.atis_web_server.pojo.DoctorModel;
import com.example.atis_web_server.service.IDoctorService;
import com.example.atis_web_server.utils.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;


/*医生管理*/
@Service
public class DoctorService extends ServiceImpl<DoctorMapper, DoctorModel> implements IDoctorService {
    @Autowired
    private DoctorMapper doctorMapper;
    private static final Logger logger = LoggerFactory.getLogger(DoctorService.class);

    @Override
    public List<DoctorModel> getUserList() {
        return doctorMapper.selectList(null);
    }

    @Override
    public RespDoctorDetail getUserDetail(Long uid) {
        RespDoctorDetail resp = new RespDoctorDetail();
        DoctorModel doctorModel = getUserByUid(uid);
        resp.setUsername(doctorModel.getUsername());
        resp.setName(doctorModel.getName());
        resp.setUid(doctorModel.getId());

        //使用JWT工具类，生成身份令牌
        resp.setToken(Utils.createToken(Math.toIntExact(doctorModel.getId())));
        return resp;
    }

    @Override
    public Long registerUser(ReqRegisterDoctor req) {
        DoctorModel doctorModel = new DoctorModel();
        doctorModel.setUsername(req.getUsername());
        doctorModel.setPassword(req.getPassword());
        doctorModel.setName(req.getName());
        doctorModel.setCreateTime(LocalDateTime.now());
        doctorModel.setLoginTime(LocalDateTime.now());
        doctorModel.setDeleted(false);

        save(doctorModel);
        return doctorModel.getId();
    }

    @Override
    public DoctorModel getUserByName(String userName) {
        QueryWrapper<DoctorModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DoctorModel::getUsername, userName);
        return doctorMapper.selectOne(queryWrapper, false);
    }

    @Override
    public DoctorModel toUserModel(ReqUpdateDoctor req) {
        DoctorModel doctorModel = getUserByUid(req.getId());
        if (ObjUtil.isEmpty(doctorModel)) {
            return null;
        }
        doctorModel.setUsername(req.getUsername());
        doctorModel.setName(req.getName());
//        doctorModel.setDeleted(req.getDeleted());  // TODO 检查是否有admin权限，没有则不能更新此项
        return doctorModel;
    }

    @Override
    public void insert(DoctorModel doctorModel) {
        doctorMapper.insert(doctorModel);
    }

    @Override
    public boolean isExistsUser(String userName) {
        QueryWrapper<DoctorModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DoctorModel::getUsername, userName);
        return doctorMapper.exists(queryWrapper);
    }

    @Override
    public boolean changePassword(int uid, String password) {
        DoctorModel doctorModel = doctorMapper.selectById(uid);
        if (ObjUtil.isEmpty(doctorModel)) {
            return false;
        }
        doctorModel.setPassword(password);
        return doctorMapper.updateById(doctorModel) > 0;
    }

    @Override
    public DoctorModel getUserByUid(Long uid) {
        return doctorMapper.selectById(uid);
    }

    @Override
    public boolean isExistsUserByUid(Long uid) {
        return !ObjUtil.isEmpty(doctorMapper.selectById(uid));
    }

    @Override
    public void delete(Long id) {
        doctorMapper.deleteById(id);
    }

    @Override
    public void setDeleted(Long id, boolean deleted) {
        DoctorModel doctorModel = doctorMapper.selectById(id);
        doctorModel.setDeleted(deleted);

        doctorMapper.updateById(doctorModel);
    }

    @Override
    public boolean updateUserInfo(DoctorModel doctorModel) {
        return doctorMapper.updateById(doctorModel) == 1;
    }


}
