package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@TableName(ConstModelNameAttribute.TaskRecord)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskRecordModel {
    @TableId(type = IdType.AUTO) //mybatis-plus注解
    @ApiModelProperty("任务记录ID")
    private Long id;
    @ApiModelProperty("任务ID：任务来源ID，由任务池中的哪个任务生成的")
    private Long tid;
    @ApiModelProperty("用户ID")
    private Long uid;
    @ApiModelProperty("任务完成状态")
    private Boolean completion = false;
    @ApiModelProperty("完成时间")
    private LocalDateTime completeTime;
    @ApiModelProperty("任务得分")
    private Integer score;
    @ApiModelProperty("备注")
    private String remark;
    @ApiModelProperty("第几天的任务")
    private Integer day;
    @ApiModelProperty("设定任务最大完成时间")
    private Integer time;
    @ApiModelProperty("是否被用于生成过新话题")
    private Boolean used = false;
    @ApiModelProperty("聊天记录")
    private String chatHistory;
    @ApiModelProperty("聊天总结：用于个人信息")
    private String chatSummary;
    @ApiModelProperty("医生ID")
    private Long doctorId;
    @ApiModelProperty("创建时间")
    private LocalDateTime createdAt;
}