package com.example.atis_web_server;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

@ServletComponentScan
@SpringBootApplication
@EnableScheduling
@MapperScan("com.example.atis_web_server.mapper")
public class AtisWebServerApplication {
    private static final Logger logger = LoggerFactory.getLogger(AtisWebServerApplication.class);

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        SpringApplication.run(AtisWebServerApplication.class, args);
        logger.info("----------------------ATIS Server Start---------------------");
    }

}
