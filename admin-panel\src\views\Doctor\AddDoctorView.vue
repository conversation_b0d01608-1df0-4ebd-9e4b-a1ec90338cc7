<template>
  <div class="addBrand-container">
    <div class="container">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px">
        <el-form-item label="账号" prop="username">
          <el-input v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="ruleForm.name"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="ruleForm.password"></el-input>
        </el-form-item>

        <div class="subBox">
          <el-button type="primary" @click="submitForm('ruleForm',false)">保存</el-button>
          <el-button type="primary" @click="submitForm('ruleForm', true)">保存并继续添加医生</el-button>
          <el-button @click="() => this.$router.push('/doctor')">返回</el-button>
        </div>
      </el-form>
    </div>
  </div>
</template>


<script lang="ts">
import {addDoctor} from '@/api/admin'
import router from '@/router';
export default {
  data() {
    return {
      ruleForm: {
        name: '',
        username: '',
        password: ''
      },
      rules: {
        name: [
            { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        username: [
            { required: true, message: '请输入账号', trigger: 'blur' }
        ],
        password: [
            { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submitForm(formName, isContinue) {
      //进行表单校验
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //表单校验通过，发起Ajax请求，将数据提交到后端
          addDoctor(this.ruleForm).then((res) => {
            if (res.data.code === 200) {
              this.$message.success('用户添加成功！')

              if (isContinue) {  //如果是保存并继续添加用户，则清空表单数据
                this.ruleForm = { //清空表单数据
                  name: '',
                  username: '',
                  gender: true,
                  phone: '',
                  birthday: ''
                }
              } else {  //返回医生列表
                this.$router.push('/doctor')
              }
            } else {
              this.$message.error(res.data.msg)
            }
          })
        }
      })
    }
  }
}
</script>




<style scoped>
.addBrand-container {
    margin: 30px;
    margin-top: 30px;
}

.HeadLable {
    background-color: transparent;
    margin-bottom: 0px;
    padding-left: 0px;
}

.container {
    position: relative;
    z-index: 1;
    background: #fff;
    padding: 30px;
    border-radius: 4px;
}

.subBox {
    padding-top: 30px;
    text-align: center;
    border-top: solid 1px #dcdcdc; /* Replaced $gray-5 with a hex color */
}

.el-form-item {
    margin-bottom: 29px;
}

.el-input {
    width: 293px;
}
</style>

  