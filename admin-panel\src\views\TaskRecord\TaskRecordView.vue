<template>
  <div>
    <!-- 搜索栏 -->
    <div class="tableBar">
      <label style="margin-right: 5px">任务名:</label>
      <el-input ref="searchFormName" v-model="searchForm.name" placeholder="请输入" style="width: 6%" />

      <label style="margin-right: 5px; margin-left: 20px">患者用户名:</label>
      <el-input v-model="searchForm.username" placeholder="请输入" style="width: 6%" />

      <label style="margin-right: 5px; margin-left: 20px">医生姓名:</label>
      <el-input v-model="searchForm.doctorName" placeholder="请输入" style="width: 6%" />

      <label style="margin-right: 5px; margin-left: 20px">任务完成状态:</label>
      <el-select v-model="searchForm.completion" placeholder="请选择" style="width: 6%">

        <el-option label="是" :value="true"></el-option>
        <el-option label="否" :value="false"></el-option>
        <el-option label="全部"></el-option>
      </el-select>

      <label style="margin-right: 5px; margin-left: 20px">第几天的任务:</label>
      <el-input v-model="searchForm.day" placeholder="输入天数" style="width: 6%" />

      <label style="margin-right: 5px; margin-left: 20px">完成时间:</label>
      <el-date-picker v-model="searchForm.completeTime" type="date" placeholder="选择日期"
        style="width: 9%"></el-date-picker>

      <label style="margin-right: 5px; margin-left: 20px">是否生成过新话题:</label>
      <el-select v-model="searchForm.used" placeholder="请选择" style="width: 6%">
        <el-option label="是" :value="true"></el-option>
        <el-option label="否" :value="false"></el-option>
        <el-option label="全部"></el-option>
      </el-select>

      <!-- 搜索按钮 -->
      <el-button type="primary" style="margin-left: 20px" @click="pageQuery">查询</el-button>
      <br><br>
      <!-- 新增按钮 -->
      <!-- <el-button type="primary" style="float: right" @click="handleAddTask">+新增</el-button> -->
    </div>
    <br><br>

    <!-- 查询到的表格数据 -->
    <el-table v-bind:data="records" stripe style="width: 100%">
      <!-- 序号 -->
      <el-table-column label="序号" width="50">
                <template slot-scope="scope">
                    {{ (pageNo - 1) * pageSize + scope.$index + 1 }}
                </template>
      </el-table-column>

      <el-table-column prop="id" label="ID" width="100">
      </el-table-column>
      <!-- <el-table-column prop="tid" label="tid" width="180">
      </el-table-column> -->
      <el-table-column prop="name" label="任务名称" width="100">
      </el-table-column>
      <el-table-column prop="username" label="用户账号" width="100">
      </el-table-column>
      <!-- <el-table-column prop="XXXXXXXXXXXXXXXXXXXXXXXXXXX" label="用户姓名" width="100">
      </el-table-column> -->
      <el-table-column prop="completion" label="是否完成" width="100">
        <template slot-scope="scope">
          {{ scope.row.completion === true ? '√' : 'x' }}
        </template>
      </el-table-column>
      <el-table-column prop="completeTime" label="完成时间" width="180">
        <template slot-scope="scope">
          {{ scope.row.completeTime ? new Date(scope.row.completeTime).toLocaleString() : '' }}
        </template>
      </el-table-column>
      <el-table-column prop="score" label="分数" width="100">
      </el-table-column>
      <el-table-column prop="remark" label="备注" width="100">
      </el-table-column>
      <el-table-column prop="day" label="第几天的任务" width="120">
      </el-table-column>
      <el-table-column prop="time" label="时长" width="120">
      </el-table-column>
      <el-table-column prop="used" label="是否使用" width="100">
        <template slot-scope="scope">
          {{ scope.row.used === true ? '√' : 'x' }}
        </template>
      </el-table-column>
      <el-table-column prop="doctorName" label="医生名" width="100">
      </el-table-column>
      <el-table-column prop="patientName" label="患者的姓名" width="100">
      </el-table-column>

      <!-- <el-table-column prop="deleted" label="任务状态">
        <template slot-scope="scope">
          {{ scope.row.deleted === true ? '已禁用' : '启用中' }}
        </template>
      </el-table-column> -->
      <!-- <el-table-column prop="taskRecordId" label="任务来源">
      </el-table-column> -->
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdateTask(scope.row)">修改</el-button>
          <!-- <el-button type="text" @click="handleStartOrStopTask(scope.row)">{{ scope.row.deleted === 1 ? '启用' : '禁用'
            }}</el-button> -->
            <el-button type="text" @click="chatHistory(scope.row)">聊天记录</el-button>
        </template>
      </el-table-column>
    </el-table>
    <br><br>

    <!-- Pagination分页 -->
    <el-pagination class="pageList" @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="pageNo" :page-sizes="[10, 20, 30, 40, 50]" :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total" style="text-align: center;">
    </el-pagination>

  </div>
</template>

<script>
import axios from 'axios';
import {getTaskRecordList, addTaskRecord, updateTaskRecord} from '@/api/task'
export default {
  data() {
    return {
      searchForm: {
        name: '', // 任务名
        username: '', // 患者用户名
        doctorName: '', // 医生姓名
        completion: '', // 任务完成状态
        day: '', // 第几天的任务
        completeTime: '', // 完成时间
        used: '', // 是否被用于生成过新话题
      },
      pageNo: 1, // 当前页码
      pageSize: 10, // 每页显示数量
      total: 0, // 总数
      records: [], // 任务列表
    }
  },
  methods: {
    created() {
      this.pageQuery();
    },
    pageQuery() {
      const params = {};
      if (this.searchForm.name) params.name = this.searchForm.name;
      if (this.searchForm.username) params.username = this.searchForm.username;
      if (this.searchForm.doctorName) params.doctorName = this.searchForm.doctorName;
      if (this.searchForm.completion !== '') params.completion = this.searchForm.completion;
      if (this.searchForm.day) params.day = this.searchForm.day;
      if (this.searchForm.completeTime) params.completeTime = this.searchForm.completeTime;
      if (this.searchForm.used !== '') params.used = this.searchForm.used;
      params.pageNo = this.pageNo;
      params.pageSize = this.pageSize;

      getTaskRecordList(params).then(resp => {
        if (resp.data.code == 200) {
          this.records = resp.data.data.list;
          this.total = resp.data.data.total;
        }
      }).catch(error => {
        this.$message.error("查询任务记录失败" + error.message);
      })
    },
    handleAddTask() {
      this.$router.push({ path: '/taskRecord/add' });  //跳转到新增页面
    },
    handleUpdateTask(row) {
      this.$router.push({ path: '/taskRecord/add', query: { id: row.id, pageNo: this.pageNo, ...this.getQueryParams() } });  //跳转到修改页面
    },
    chatHistory(row) {
      // alert('查看聊天记录');
      this.$router.push({ path: '/taskRecord/chatHistory', query: { id: row.id, pageNo: this.pageNo, ...this.getQueryParams() } });  //跳转到聊天记录页面
    },
    getQueryParams() {
            return {
                name: this.searchForm.name,
                username: this.searchForm.username,
                doctorName: this.searchForm.doctorName,
                completion: this.searchForm.completion,
                day: this.searchForm.day,
                completeTime: this.searchForm.completeTime,
                used: this.searchForm.used
            };
    },
    // handleStartOrStopTask(row) {
    //   // 启用或禁用任务逻辑
    // },
    // getTaskType(typeId) {
    //   switch (typeId) {
    //     case 0:
    //       return '命名任务';
    //     case 1:
    //       return '提要求任务';
    //     case 2:
    //       return '对话任务';
    //     default:
    //       return '未知任务';
    //   }
    // },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
      this.pageQuery()
    },
    handleCurrentChange(pageNo) {
      this.pageNo = pageNo
      this.pageQuery()
    },
  },
  mounted() {
    // 处理路由参数，恢复查询条件
    const query = this.$route.query;
    // if (query.searchParams) {
      // const parsedSearchParams = JSON.parse(query.searchParams);
      // this.searchForm.name = parsedSearchParams.name || '';
      // this.searchForm.username = parsedSearchParams.username || '';
      // this.searchForm.doctorName = parsedSearchParams.doctorName || '';
      // this.searchForm.completion = parsedSearchParams.completion || '';
      // this.searchForm.day = parsedSearchParams.day || '';
      // this.searchForm.completeTime = parsedSearchParams.completeTime || '';
      // this.searchForm.used = parsedSearchParams.used || '';
    // }
    if (query.pageNo) {
      this.pageNo = Number(query.pageNo);
    }
    this.searchForm.name = query.name || '';
    this.searchForm.username = query.username || '';
    this.searchForm.doctorName = query.doctorName || '';
    this.searchForm.completion = query.completion || '';
    this.searchForm.day = query.day || '';
    this.searchForm.completeTime = query.completeTime || '';
    this.searchForm.used = query.used || '';
    this.pageQuery();
  }
}
</script>

<style>
</style>