package com.example.atis_web_server.dto.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@NoArgsConstructor
@AllArgsConstructor
@ToString
@Data
@ApiModel(description = "登录数据统计")
public class RespDataStatistics {
    @ApiModelProperty("次数")
    private Integer count;
    @ApiModelProperty("时间戳")
    private Long timestamp;
}
