import pymysql
import json

# 将默认任务从json迁移到数据库中

# JSON数据
json_data = '''
[
	{"topic":"动物","hint":"养宠物，关于动物的知识，动物的声音和习性，如果你变成一种动物会做什么"},
	{"topic":"食物","hint":"最喜欢的食物，制作简单的食物（如三明治），奇怪的食物组合"},
	{"topic":"家人","hint":"家庭成员介绍，家庭活动或假期回忆，最喜欢的家人故事"},
	{"topic":"玩具","hint":"最喜欢的玩具，分享玩具的故事，如何制作自己的玩具"},
	{"topic":"颜色","hint":"最喜欢的颜色，颜色的混合（比如红色加黄色变成橙色），颜色与情感的关系"},
	{"topic":"朋友","hint":"最好的朋友是谁，给朋友起昵称，喜欢和朋友一起做什么，怎么交上朋友的"},
	{"topic":"生日","hint":"生日派对的计划，收到的礼物，生日愿望"},
	{"topic":"运动","hint":"最喜欢的运动，有哪些运动，运动的知识，奥运会的知识，喜欢的运动员"},
	{"topic":"节日","hint":"最喜欢的节日，节日的传统习俗，节日的食物和活动，上一个节日是什么，最近的下一个节日"},
	{"topic":"韵律、儿歌、乐器和音乐","hint":"最喜欢的歌曲，最喜欢的乐器，学习乐器的经历，自己创作一支简单的歌曲"},
	{"topic":"动画片","hint":"最喜欢的动画片角色，动画片的情节，如果你是这个角色会做什么"},
	{"topic":"电影和电视节目","hint":"最喜欢的电影，角色扮演，电影中的有趣情节，观看的时间"},
	{"topic":"科技和发明","hint":"最新的科技，想象未来的科技，想要发明的东西，科技如何改变生活，有名的发明家，想去多久之后的未来"},
	{"topic":"冒险故事","hint":"最喜欢的童话故事、冒险故事或传说故事，谁讲的故事，故事中最喜欢的角色，传说中的生物，自己编一个冒险故事，想去哪里冒险，想和谁去冒险（可以是虚构人物）"},
	{"topic":"超级英雄","hint":"最喜欢的超级英雄，想要获得哪种超能力，如果你有超能力会做什么，想见到哪个超级英雄，想和超级英雄说什么"},
	{"topic":"自然景观","hint":"喜欢的自然景观，如海滩或森林，自然中的动物和植物"},
	{"topic":"职业","hint":"长大后想做什么，各种职业的介绍，想要体验某个职业的一天"},
	{"topic":"交通工具","hint":"最喜欢的交通工具，交通工具的功能，交通工具的知识，搭乘交通工具应该怎么做，自己设计一辆车"},
	{"topic":"太空和星星","hint":"太空探险，星座和星星，宇宙的知识，宇航员的生活，太空中能不能呼吸，月球的知识"},
	{"topic":"海洋","hint":"喜欢的海洋动物，海里有什么，海底探险或是海上冒险的故事"},
	{"topic":"恐龙","hint":"喜欢的恐龙，是否害怕恐龙，各种恐龙的知识，如果世界上还有恐龙会怎样，恐龙的灭绝原因"},
	{"topic":"植物和花朵","hint":"喜欢的花朵或植物，如何种植简单的植物，植物的生长过程，花朵有哪些颜色"},
	{"topic":"天气、季节和节气","hint":"最喜欢的季节，天气现象（如彩虹），各个季节的特点，生日所在的季节"},
	{"topic":"文化和传统","hint":"中国的文化和传统，自己国家的传统，有趣的民间故事"},
	{"topic":"舞蹈、美术和手工艺","hint":"舞蹈的知识，学习舞蹈的方法，最喜欢的美术作品，制作简单的手工作品，画画的快乐"},
	{"topic":"旅游","hint":"最喜欢的旅游景点，外出的有趣经历，想去的地方"},
	{"topic":"博物馆、水族馆、公园和游乐场","hint":"喜欢的展览，博物馆里的发现，水族馆里的生物，最喜欢的公园，游乐场的活动，喜欢玩的游乐设施"},
	{"topic":"日常习惯、健康和卫生","hint":"一天的作息时间，好的生活习惯，做家务帮忙，洗手，什么样的饮食是健康的，个人卫生习惯，锻炼的重要性，保护环境"},
	{"topic":"安全意识","hint":"家庭安全，户外安全，哪些情况是紧急情况，紧急情况下该怎么办，向警察求助和拨打报警电话，怎么过马路"},
	{"topic":"爱好和兴趣","hint":"最喜欢的爱好，如何培养兴趣，分享爱好带来的快乐"},
	{"topic":"其他国家","hint":"七大洲五大洋，相对中国的位置，不同的文化，如何和世界各地的小朋友交朋友"}
  ]
'''

# 解析JSON数据
data = json.loads(json_data)

try:
    # 连接数据库
    conn = pymysql.connect(
        host="localhost",
        port=3306,
        user="root",
        password="123456",
        database="atis",
        charset='utf8mb4'
    )
    cursor = conn.cursor()
    # # 使用execute方法执行SQL语句
    # cursor.execute("SELECT VERSION()")
    #
    # # 使用 fetchone() 方法获取一条数据
    # data = cursor.fetchone()
    # print(data)
    # 插入数据
    for item in data:
        # 插入语句中包含所有字段，未指定的字段将使用默认值
        cursor.execute("INSERT INTO task_default (topic, hint, deleted, remark) VALUES (%s, %s, %s, %s)",
                      (item['topic'], item['hint'], 0, None))

    # 提交事务
    conn.commit()
except pymysql.Error as err:
    print("Something went wrong: {}".format(err))
finally:
    # 关闭连接
    cursor.close()
    conn.close()
    print("MySQL connection is closed")