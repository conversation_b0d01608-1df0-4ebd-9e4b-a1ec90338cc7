package com.example.atis_web_server.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@RestController
public class BgLogController {
    @GetMapping("/logs")
    public List<String> getLogs(@RequestParam String date) {
        List<String> logLines = new ArrayList<>();
        String logFilePath = "logs/app-" + date + ".log";
        try (BufferedReader reader = new BufferedReader(new FileReader(logFilePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                logLines.add(line);
            }
        } catch (IOException e) {
            logLines.add("Error reading log file: " + e.getMessage());
        }
        return logLines;
    }
}