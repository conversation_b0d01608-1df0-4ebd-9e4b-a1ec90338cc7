package com.example.atis_web_server.dto.query;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "分页查询实体")
public class PageQuery {
    @ApiModelProperty("页码")
    private Integer pageNo = 1;
    @ApiModelProperty("一页的数量")
    private Integer pageSize = 5;
    //    @ApiModelProperty("排序字段")
//    private List<String> sortsBy;
//    @ApiModelProperty("是否升序")
//    private List<Boolean> issAsc;
    @ApiModelProperty("排序字段, 是否升序")
    private List<OrderItem> orderItems;

    public <T> Page<T> toMpPage() {
        // 1.分页条件
        Page<T> page = Page.of(pageNo, pageSize);
        // 2.排序条件
        if (orderItems != null) {
            for (OrderItem orderItem : orderItems) {
                page.addOrder(orderItem);
            }
        }
        return page;
    }

}
