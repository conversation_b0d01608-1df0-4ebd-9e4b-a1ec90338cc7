{"name": "atis_server", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@types/md5": "^2.3.5", "axios": "^1.7.7", "core-js": "^3.8.3", "element-ui": "^2.15.3", "js-cookie": "^3.0.5", "md5": "^2.3.0", "nprogress": "^0.2.0", "vue": "^2.6.14", "vue-class-component": "^7.2.6", "vue-property-decorator": "^9.1.2", "vue-router": "^3.5.1", "vuex": "^3.6.2", "vuex-module-decorators": "^2.0.0"}, "devDependencies": {"@types/jest": "^29.5.13", "@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "@types/vue": "^2.0.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "typescript": "^5.6.2", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}