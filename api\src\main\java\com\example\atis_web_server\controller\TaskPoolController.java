package com.example.atis_web_server.controller;

import cn.hutool.core.util.ObjUtil;
import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.dto.req.ReqTaskPoolInfo;
import com.example.atis_web_server.dto.req.ReqUpdateTaskPool;
import com.example.atis_web_server.dto.req.ReqUserInfo;
import com.example.atis_web_server.pojo.TaskPoolModel;
import com.example.atis_web_server.service.impl.TaskPoolService;
import com.example.atis_web_server.utils.Utils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


@Slf4j
@RestController
@Api(tags = "后台任务池管理接口")
@RequestMapping(value = "/Background/TaskPool")
public class TaskPoolController {
    @Autowired
    private TaskPoolService taskPoolService;

    private static final Logger logger = LoggerFactory.getLogger(TaskPoolController.class);

    @ApiOperation("修改单个任务池中的任务")
    @PostMapping("/update")
    public Result updateTaskPoolInfo(@Valid @RequestBody ReqUpdateTaskPool req) throws Exception {
        if (ObjUtil.isEmpty(taskPoolService.getById(req.getId()))) {
            return Result.error(404, "任务不存在");
        }

        TaskPoolModel taskPoolModel = taskPoolService.toTaskPoolModel(req);
        boolean result = taskPoolService.updateById(taskPoolModel);
        if (result) {
            return Result.success(taskPoolModel);
        } else {
            return Result.error(423, "更新失败");
        }
    }

    @ApiOperation("根据复杂条件分页查询任务池接口")
    @GetMapping("/queryPage")
    public Result queryTaskPoolPage(ReqTaskPoolInfo query, @RequestHeader(Utils.TOKEN) String token) {
        Long doctorId = Utils.getUidByToken(token);
        logger.info("/queryPage; doctorId  = {}", doctorId);
        return Result.success(taskPoolService.queryTaskPoolPage(query, doctorId));
    }

    /**
     * 查询单个任务 主要是更新任务时，需要返回单个任务的部分信息
     */
    @ApiOperation("查询单个任务 - 后台")
    @PostMapping("/info")
    public Result getTaskPoolInfo(@Valid @RequestBody ReqUserInfo req) throws Exception {
        Long id = req.getUid();
        if (taskPoolService.isExistsTaskRPoolById(id)) {
            return Result.success(taskPoolService.getById(id));
        } else {
            return Result.error(404, "该任务不存在");
        }
    }

    /**
     * 更新任务池的默认任务
     */
    @ApiOperation("更新任务池的默认任务 - 后台")
    @PostMapping("/updateDefaultTask")
    public Result updateDefaultTaskPool() throws Exception {
        taskPoolService.updateDefaultTaskPool();
        return Result.success();
    }
}
