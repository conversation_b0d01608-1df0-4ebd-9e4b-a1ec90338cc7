package com.example.atis_web_server.common;

import java.util.*;
import java.util.stream.Collectors;

public class ListHandle {

    /**
     * 从列表中随机选择指定数量的元素。
     *
     * @param list  原始列表
     * @param count 要选择的元素数量
     * @return 新的列表，包含选定的元素
     */
    public static <T> List<T> selectRandomElements(List<T> list, int count) {
        if (list == null || list.isEmpty() || count <= 0) {
            return new ArrayList<>(); // 返回空列表
        }

        // 创建一个副本，避免修改原始列表
        List<T> copyList = new ArrayList<>(list);

        // 打乱列表顺序
        Collections.shuffle(copyList, new Random());

        // 截取前count个元素
        return new ArrayList<>(copyList.subList(0, Math.min(count, copyList.size())));
    }

    @SafeVarargs
    public static <T> List<T> mergeLists(List<T>... lists) {
        return Arrays.stream(lists)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }
}
