package com.example.atis_web_server.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.example.atis_web_server.mapper.ConfigMapper;
import com.example.atis_web_server.pojo.ConfigModel;
import com.example.atis_web_server.service.IConfigService;
import com.example.atis_web_server.utils.RSAEncryptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.security.PublicKey;

@Service
public class ConfigService extends ServiceImpl<ConfigMapper, ConfigModel> implements IConfigService {
    @Autowired
    private ConfigMapper configMapper;
    private static final Logger logger = LoggerFactory.getLogger(ConfigService.class);

    @PostConstruct
    public void init() throws Exception {
        // 在这里执行你的任务
        logger.info("ConfigService 已初始化，执行启动时任务...");
    }

    @Override
    public ConfigModel getInfo() {
        return configMapper.selectList(null).get(0);
    }
}
