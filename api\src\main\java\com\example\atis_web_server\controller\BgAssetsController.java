package com.example.atis_web_server.controller;

import com.example.atis_web_server.common.Result;
import com.example.atis_web_server.dto.req.ReqUpdateAssets;
import com.example.atis_web_server.service.impl.AssetsUpdatingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;


@Api(tags = "后台资源更新管理接口")
@Slf4j
@RestController
@Scope("prototype")
@RequestMapping(value = "/Background/AssetsUpdating")
public class BgAssetsController {
    @Autowired
    private AssetsUpdatingService assetsUpdatingService;

    private static final Logger logger = LoggerFactory.getLogger(BgAssetsController.class);

    @ApiOperation("获取 资源更新 的信息")
    @GetMapping(value = "/getAssetsInfo")
    public Object getAssetsInfo() throws Exception {
        return Result.success(assetsUpdatingService.queryPage());
    }

    @ApiOperation("更新 资源更新 的信息")
    @PostMapping(value = "/updateAssetsInfo")
    public Result updateAssetsInfo(ReqUpdateAssets req) {
        logger.info("***** updateAssetsInfo *****");
        return Result.success(assetsUpdatingService.toAssetsUpdatingModel(req));
    }
}
