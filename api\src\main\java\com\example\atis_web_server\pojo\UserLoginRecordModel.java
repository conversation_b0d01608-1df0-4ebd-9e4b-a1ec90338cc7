package com.example.atis_web_server.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@TableName(ConstModelNameAttribute.UserLoginRecord)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserLoginRecordModel {
    @TableId(type = IdType.AUTO) //mybatis-plus注解
    @ApiModelProperty("登录记录ID")
    private Long id;
    @ApiModelProperty("用户ID")
    private Long uid;
    @ApiModelProperty("登录时间")
    private LocalDateTime loginTime;
    @ApiModelProperty("登录地点")
    private String place;
    @ApiModelProperty("登录设备")
    private String device;
}