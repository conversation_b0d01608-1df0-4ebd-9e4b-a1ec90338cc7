import Cookies from 'js-cookie';

// App
// 定义侧边栏状态的键名
const sidebarStatusKey = 'sidebar_status';
// 获取侧边栏状态
export const getSidebarStatus = () => Cookies.get(sidebarStatusKey);
// 设置侧边栏状态
export const setSidebarStatus = (sidebarStatus: string) => Cookies.set(sidebarStatusKey, sidebarStatus);

// User
// 定义商店ID的键名
const storeId = 'storeId';
// 获取商店ID
export const getStoreId = () => Cookies.get(storeId);
// 设置商店ID
export const setStoreId = (id: string) => Cookies.set(storeId, id);
// 移除商店ID
export const removeStoreId = () => Cookies.remove(storeId);

// User
// 定义令牌的键名
const tokenKey = 'token';
// 获取令牌
export const getToken = () => Cookies.get(tokenKey);
// 设置令牌
export const setToken = (token: string) => Cookies.set(tokenKey, token);
// 移除令牌
export const removeToken = () => Cookies.remove(tokenKey);

// userInfo
// 定义用户信息的键名
const userInfoKey = 'userInfo';
// 获取用户信息
export const getUserInfo = () => Cookies.get(userInfoKey);
// 设置用户信息
export const setUserInfo = (useInfor: Object) => Cookies.set(userInfoKey, JSON.stringify(useInfor));
// 移除用户信息
export const removeUserInfo = () => Cookies.remove(userInfoKey);

// printinfo
// 定义打印信息的键名
const printKey = 'print';
// 获取打印信息
export const getPrint = () => Cookies.get(printKey);
// 设置打印信息
export const setPrint = (useInfor: Object) => Cookies.set(printKey, JSON.stringify(useInfor));
// 移除打印信息
export const removePrint = () => Cookies.remove(printKey);

// 获取消息
// 定义新数据的键名
const newData = 'new';
// 获取新数据
export const getNewData = () => Cookies.get(newData);
// 设置新数据
export const setNewData = (val: Object) => Cookies.set(newData, JSON.stringify(val));
